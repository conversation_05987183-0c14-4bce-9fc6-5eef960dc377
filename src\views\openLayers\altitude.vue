<script setup>
import { ref, onMounted, onActivated, onUnmounted } from "vue";
import "ol/ol.css";
import TileLayer from "ol/layer/Tile";
import XYZ from "ol/source/XYZ";
import * as GeoTIFF from "geotiff";
import { Map, View } from "ol";
import { ElMessage } from "element-plus";
import { fromLonLat, toLonLat } from "ol/proj";
import axios from "axios";

const map = ref(null);
const geoTiffCache = ref(null); // 缓存已加载的 GeoTIFF 数据
const elevation = ref(null);
const error = ref(null);

// 加载 GeoTIFF 文件并缓存结果
async function loadGeoTIFF(url) {
  try {
    // 如果缓存可用，直接返回缓存数据
    if (geoTiffCache.value) {
      return geoTiffCache.value;
    }

    console.log("正在加载 GeoTIFF 文件:", url);
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取 GeoTIFF 失败: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const tiff = await GeoTIFF.fromArrayBuffer(arrayBuffer);
    const image = await tiff.getImage();

    // 获取图像元数据
    const width = image.getWidth();
    const height = image.getHeight();
    const [minX, minY, maxX, maxY] = image.getBoundingBox();
    const metadata = image.getFileDirectory();

    // 缓存结果以避免重复加载
    geoTiffCache.value = {
      tiff,
      image,
      width,
      height,
      minX,
      minY,
      maxX,
      maxY,
      metadata,
    };

    console.log("GeoTIFF 加载成功:", { width, height, boundingBox: [minX, minY, maxX, maxY] });
    return geoTiffCache.value;
  } catch (error) {
    console.error("加载 GeoTIFF 文件失败:", error);
    ElMessage.error(`加载高程数据失败: ${error.message}`);
    return null;
  }
}

// 使用双线性插值获取特定坐标的高程数据
// async function getElevation(coordinate) {
//   try {
//     // 如果尚未加载 GeoTIFF，则先加载
//     const tiffData = await loadGeoTIFF("/JS.tif");
//     if (!tiffData) {
//       return null;
//     }

//     const { image, width, height, minX, minY, maxX, maxY } = tiffData;

//     // 确保坐标在边界范围内
//     const [lon, lat] = coordinate;
//     if (lon < minX || lon > maxX || lat < minY || lat > maxY) {
//       console.warn("坐标超出 GeoTIFF 边界范围:", coordinate, "边界:", [minX, minY, maxX, maxY]);
//       return null;
//     }

//     // 计算浮点像素坐标
//     const pixelXFloat = ((lon - minX) / (maxX - minX)) * (width - 1);
//     const pixelYFloat = ((maxY - lat) / (maxY - minY)) * (height - 1);

//     // 获取周围像素的整数坐标
//     const x0 = Math.floor(pixelXFloat);
//     const y0 = Math.floor(pixelYFloat);
//     const x1 = Math.min(x0 + 1, width - 1);
//     const y1 = Math.min(y0 + 1, height - 1);

//     // 计算插值所需的小数部分
//     const xFrac = pixelXFloat - x0;
//     const yFrac = pixelYFloat - y0;

//     // 读取包含四个周围像素的窗口
//     const window = [Math.max(0, x0), Math.max(0, y0), Math.min(x1 + 1, width), Math.min(y1 + 1, height)];

//     // 读取栅格数据
//     const rasterData = await image.readRasters({
//       window,
//       samples: [0], // 仅读取第一个波段
//     });

//     if (!rasterData || !rasterData[0]) {
//       console.error("读取栅格数据失败");
//       return null;
//     }

//     // 获取四个周围像素的值
//     const windowWidth = window[2] - window[0];
//     const getValue = (x, y) => {
//       const localX = x - window[0];
//       const localY = y - window[0];
//       const index = localY * windowWidth + localX;
//       return rasterData[0][index];
//     };

//     const topLeft = getValue(x0, y0);
//     const topRight = getValue(x1, y0);
//     const bottomLeft = getValue(x0, y1);
//     const bottomRight = getValue(x1, y1);

//     // 执行双线性插值
//     const top = topLeft * (1 - xFrac) + topRight * xFrac;
//     const bottom = bottomLeft * (1 - xFrac) + bottomRight * xFrac;
//     const elevation = top * (1 - yFrac) + bottom * yFrac;

//     return elevation;
//   } catch (error) {
//     console.error("获取高程失败:", error);
//     return null;
//   }
// }

// 优化版本，只读取必要的数据（最近邻法）
async function getElevationFast(coordinate) {
  try {
    // 加载 GeoTIFF（如果尚未加载）
    const tiffData = await loadGeoTIFF("/JS.tif");
    if (!tiffData) {
      return null;
    }

    const { image, width, height, minX, minY, maxX, maxY } = tiffData;

    // 确保坐标在边界范围内
    const [lon, lat] = coordinate;
    if (lon < minX || lon > maxX || lat < minY || lat > maxY) {
      console.warn("坐标超出 GeoTIFF 边界范围");
      return null;
    }

    // 计算像素坐标（最近邻方法）优化方案
    const pixelX = Math.floor(((lon - minX) / (maxX - minX)) * (width - 1));
    const pixelY = Math.floor(((maxY - lat) / (maxY - minY)) * (height - 1));

    // 只读取我们需要的一个像素
    const rasterData = await image.readRasters({
      window: [pixelX, pixelY, pixelX + 1, pixelY + 1],
      samples: [0], // 仅第一个波段
    });

    if (!rasterData || !rasterData[0] || rasterData[0].length === 0) {
      console.error("读取高程数据失败");
      return null;
    }

    return rasterData[0][0];
  } catch (error) {
    console.error("获取高程失败:", error);
    return null;
  }
}

// 在地图点击时获取高程
function addMapClickHandler() {
  if (!map.value) return;
  map.value.on("singleclick", async (event) => {
    const coordinate = event.coordinate; // 因为地图投影是 EPSG:4326，坐标已经是经纬度

    try {
      const elevation = await getElevationFast(coordinate);
      if (elevation !== null) {
        ElMessage.success(
          `坐标 ${coordinate[0].toFixed(5)}, ${coordinate[1].toFixed(5)} 的高程为: ${elevation.toFixed(2)} 米`
        );
        console.log("高程:", elevation);
      } else {
        ElMessage.warning("该位置没有可用的高程数据");
      }
    } catch (error) {
      console.error("处理点击事件时出错:", error);
      ElMessage.error("获取高程数据失败");
    }
  });
  console.log("已添加地图点击处理程序");
}

const fetchElevation = async () => {
  const lon = 118.47999277041986; // 示例经度
  const lat = 32.008834170822404; // 示例纬度
  try {
    const response = await axios.get(`http://localhost:3001/api/getElevation?lon=${lon}&lat=${lat}`);
    elevation.value = response.data.elevation;
    error.value = null;
  } catch (error) {
    if (error.response) {
      error.value = error.response.data.error;
    } else {
      error.value = "请求出错，请稍后重试";
    }
    elevation.value = null;
  }
};

onMounted(async () => {
  try {
    // 初始化地图
    initMap();
    // 预加载 GeoTIFF 数据
    loadGeoTIFF("/JS.tif").then(() => {
      console.log("GeoTIFF 数据预加载完成");
      addMapClickHandler();
      // 使用示例
      getElevationFast([118.48039391336984, 32.00826737961303]).then((elevation) => {
        console.log("示例高程:", elevation);
      });
    });
  } catch (error) {
    ElMessage.error("初始化地图失败");
    console.error("初始化错误:", error);
  }
});

const initMap = () => {
  map.value = new Map({
    target: "map",
    layers: [
      new TileLayer({
        source: new XYZ({
          url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
          attribution: "Esri World Imagery",
        }),
      }),
    ],
    view: new View({
      projection: "EPSG:4326", // 直接使用 WGS84 以便于坐标处理
      center: [118.481, 32.01],
      zoom: 16,
      maxZoom: 17,
    }),
  });
};

onUnmounted(() => {
  // 如果需要，清理地图实例
  if (map.value) {
    map.value.setTarget(null);
    map.value = null;
  }
});

// 导出函数以供外部使用
defineExpose({
  // getElevation,
  getElevationFast,
});
</script>

<template>
  <div id="map">
    <div class="elevation-info">
      <el-button @click="fetchElevation">获取高程数据</el-button>
      <p v-if="elevation">高程数据: {{ elevation }}</p>
      <p v-else-if="error">{{ error }}</p>
    </div>
  </div>
</template>

<style scoped>
#map {
  height: 100%;
  width: 100%;
}

.elevation-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 4px;
  z-index: 1000;
}
</style>
