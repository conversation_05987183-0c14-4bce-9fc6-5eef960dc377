<template>
  <div ref="mapContainer" class="w-full h-full">
    <div class="absolute top-4 left-4 bg-white/80 p-3 rounded shadow-lg">
      <div class="mb-2">
        <label class="mr-2">经度:</label>
        <input
          v-model.number="radarPosition.lon"
          type="number"
          step="0.001"
          class="w-24 border border-gray-300 rounded px-2 py-1"
        />
      </div>
      <div class="mb-2">
        <label class="mr-2">纬度:</label>
        <input
          v-model.number="radarPosition.lat"
          type="number"
          step="0.001"
          class="w-24 border border-gray-300 rounded px-2 py-1"
        />
      </div>
      <div>
        <button @click="updateRadarPosition" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded">
          更新位置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from "vue";
import * as Cesium from "cesium";
import { Color, Cartesian3, Transforms, EllipsoidTerrainProvider, UrlTemplateImageryProvider } from "cesium";
import { radarDetectionArea } from "@/utils/radarPower.js";

const mapContainer = ref(null);
let viewer = null;
let radarInstances = null;
let radarPrimitive = null;
let geometryInstances = [];

// 雷达位置数据（响应式）
const radarPosition = reactive({
  lon: 118.8,
  lat: 32.05,
  height: 0,
});

// 威力区配置
const radarConfig = {
  nRow: 181,
  nCol: 361,
  R_max: radarDetectionArea.data.R_max,
};

// 缓存计算结果
let cachedGrid = null;
let enuMatrix = null;

function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";

  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));

  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8,
    })
  );
}

// 计算威力区网格点（只计算相对位置）
function computeRelativeSurfacePoints(nRow, nCol, R_max) {
  const positions2D = Array(nRow);

  for (let i = 0; i < nRow; i++) {
    positions2D[i] = new Array(nCol);
    const eleDeg = (i / (nRow - 1)) * 180 - 90;
    const eleRad = Cesium.Math.toRadians(eleDeg);
    const cosE = Math.cos(eleRad);
    const sinE = Math.sin(eleRad);

    for (let j = 0; j < nCol; j++) {
      const azRad = Cesium.Math.toRadians((j / (nCol - 1)) * 360);
      const range = R_max[i][j];

      // 计算相对ENU坐标（以雷达为原点）
      positions2D[i][j] = new Cartesian3(range * cosE * Math.sin(azRad), range * cosE * Math.cos(azRad), range * sinE);
    }
  }

  return positions2D;
}

// 根据当前位置和缓存的相对网格计算实际位置
function updateGridPositions() {
  const { lon, lat, height } = radarPosition;

  // 计算新的转换矩阵
  const radarCartesian = Cartesian3.fromDegrees(lon, lat, height);
  enuMatrix = Transforms.eastNorthUpToFixedFrame(radarCartesian);

  // 更新所有点的位置
  const worldGrid = Array(radarConfig.nRow);

  for (let i = 0; i < radarConfig.nRow; i++) {
    worldGrid[i] = new Array(radarConfig.nCol);
    for (let j = 0; j < radarConfig.nCol; j++) {
      // 将相对坐标转换为世界坐标
      worldGrid[i][j] = new Cartesian3();
      Cesium.Matrix4.multiplyByPoint(enuMatrix, cachedGrid[i][j], worldGrid[i][j]);
    }
  }

  return worldGrid;
}

// 创建雷达威力区图元
function createRadarCoverage() {
  // 计算并缓存相对位置
  cachedGrid = computeRelativeSurfacePoints(radarConfig.nRow, radarConfig.nCol, radarConfig.R_max);

  // 创建初始网格
  const grid = updateGridPositions();

  // 创建图元实例集合
  geometryInstances = [];
  const lineAppearance = new Cesium.PolylineMaterialAppearance({
    material: Cesium.Material.fromType("Color", { color: Color.YELLOW.withAlpha(0.4) }),
  });

  // 创建等仰角环线
  for (let i = 0; i < radarConfig.nRow; i++) {
    const pts = grid[i].slice();
    pts.push(grid[i][0]); // 闭合环

    const geometry = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    const instance = new Cesium.GeometryInstance({
      geometry: geometry,
      id: `elevation-ring-${i}`,
    });

    geometryInstances.push(instance);
  }

  // 创建方位径向线
  for (let j = 0; j < radarConfig.nCol; j++) {
    const pts = [];
    for (let i = 0; i < radarConfig.nRow; i++) {
      pts.push(grid[i][j]);
    }

    const geometry = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    const instance = new Cesium.GeometryInstance({
      geometry: geometry,
      id: `azimuth-line-${j}`,
    });

    geometryInstances.push(instance);
  }

  // 创建单一图元包含所有线
  radarPrimitive = new Cesium.Primitive({
    geometryInstances: geometryInstances,
    appearance: lineAppearance,
    asynchronous: false,
  });

  viewer.scene.primitives.add(radarPrimitive);
}

// 更新雷达位置
function updateRadarPosition() {
  if (!viewer || !radarPrimitive || !cachedGrid) return;

  // 计算新的网格位置
  const grid = updateGridPositions();

  // 更新所有环线
  for (let i = 0; i < radarConfig.nRow; i++) {
    const pts = grid[i].slice();
    pts.push(grid[i][0]); // 闭合环

    const geometryInstance = geometryInstances[i];
    // 创建新的Geometry实例
    const newGeometry = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    geometryInstance.geometry = newGeometry;
  }

  // 更新所有径向线
  for (let j = 0; j < radarConfig.nCol; j++) {
    const pts = [];
    for (let i = 0; i < radarConfig.nRow; i++) {
      pts.push(grid[i][j]);
    }

    const index = radarConfig.nRow + j; // 径向线在数组中的位置
    const geometryInstance = geometryInstances[index];
    // 创建新的Geometry实例
    const newGeometry = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    geometryInstance.geometry = newGeometry;
  }

  // 重新创建Primitive
  viewer.scene.primitives.remove(radarPrimitive);

  const lineAppearance = new Cesium.PolylineMaterialAppearance({
    material: Cesium.Material.fromType("Color", { color: Color.YELLOW.withAlpha(0.4) }),
  });

  radarPrimitive = new Cesium.Primitive({
    geometryInstances: geometryInstances,
    appearance: lineAppearance,
    asynchronous: false,
  });

  viewer.scene.primitives.add(radarPrimitive);
}

// 对外提供的更新位置方法
const setRadarPosition = (lon, lat, height = 0) => {
  radarPosition.lon = lon;
  radarPosition.lat = lat;
  radarPosition.height = height;
  updateRadarPosition();
};

// 在 onMounted 中调用
onMounted(() => {
  initMap();
  createRadarCoverage();
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(radarPosition.lon, radarPosition.lat, 50000),
    orientation: { heading: 0, pitch: -Math.PI / 4, roll: 0 },
  });
});

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
});

// 导出公共方法
defineExpose({
  setRadarPosition,
});
</script>
