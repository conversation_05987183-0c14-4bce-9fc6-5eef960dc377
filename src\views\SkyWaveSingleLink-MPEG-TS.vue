<template>
  <canvas ref="canvas" style="width: 800px; height: 450px; background: #000"></canvas>
</template>

<script setup>
import { ref, onMounted } from "vue";
import JSMpeg from "jsmpeg";

const canvas = ref(null);
const wsUrl = "ws://your.server:9999/stream"; // 后端 WS 地址

onMounted(() => {
  if (!canvas.value) return;
  new JSMpeg.Player(wsUrl, {
    canvas: canvas.value,
    autoplay: true,
    audio: false, // 如果也传了 AAC 音频，可设为 true
  });
});
</script>
