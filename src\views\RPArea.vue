<!-- FrequencyPlanningPage.vue -->
<script setup>
import SpecChart from "@/components/SpecChart/index.vue";
import { ref, reactive, computed, onMounted } from "vue";

// 3. 共用的参数
const categories = ref([
  "链路安全备用",
  "F2型D2-D1上行链路",
  "F1型C2-C1上行链路",
  "F2型B2-B1上行链路",
  "F1型A2-A1上行链路",
  "F2型D1-D2下行链路",
  "F1型C1-C2下行链路",
  "F2型B1-B2下行链路",
  "F1型A1-A2下行链路",
]);
const xAxisMin = ref(1.3e9);
const xAxisMax = ref(1.51e9);

// 协同
const collaborationRanges = reactive({
  available: [
    [],
    [[1.44e9, 1.45e9]],
    [[1.36e9, 1.37e9]],
    [[1.4e9, 1.41e9]],
    [[1.32e9, 1.33e9]],
    [[1.42e9, 1.43e9]],
    [],
    [[1.38e9, 1.39e9]],
    [],
  ],
  disabled: [[], [], [[3e9, 5e9]], [], [], [], [], [], []],
  conflict: [[], [], [], [], [], [], [[1.34e9, 1.35e9]], [], [[1.34e9, 1.35e9]]],
  reserved: [[[1.46e9, 1.51e9]], [], [], [], [], [], [], [], []],
  protected: [
    [],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
    [[1.3e9, 1.31e9]],
  ],
});

const padCollaborationRanges = computed(() => {
  const len = categories.value.length;
  return {
    available: pad(collaborationRanges.available, len),
    disabled: pad(collaborationRanges.disabled, len),
    conflict: pad(collaborationRanges.conflict, len),
    reserved: pad(collaborationRanges.reserved, len),
    protected: pad(collaborationRanges.protected, len),
  };
});

// 5. pad：保证每个数组长度等于 categories.length
function pad(arr, len) {
  const a = arr.slice();
  while (a.length < len) a.push([]);
  return a;
}

// 7. 定义要渲染的图列表
const chartList = computed(() => [
  {
    id: "coordination",
    title: "协同频谱频段统计",
    categories: categories.value,
    ranges: padCollaborationRanges.value,
  },
]);

const demoData = [
  {
    freqUseDeatils: [
      {
        bandWidth: 10000000,
        centerFreq: 1305000000,
        endFreq: 1310000000,
        linkName: "无人机A号-电台A1-电台A2-下行",
        num: 1,
        power: 30,
        sourceEquipmentId: "1",
        sourceEquipmentName: "无人机A号-电台A1",
        sourcePlatformId: "11",
        sourcePlatformModel: "1",
        sourcePlatformName: "无人机A号",
        startFreq: 1300000000,
        targetEquipmentId: "2",
        targetEquipmentName: "电台A2",
        targetPlatformId: "50",
        targetPlatformModel: "3",
        targetPlatformName: "测控车",
      },
    ],
    name: "无人机A号-电台A1-电台A2-下行",
    num: 1,
  },
  {
    freqUseDeatils: [
      {
        bandWidth: 10000000,
        centerFreq: 1305000000,
        endFreq: 1310000000,
        linkName: "无人机C号-电台C1-电台C2-下行",
        num: 3,
        power: 150,
        sourceEquipmentId: "5",
        sourceEquipmentName: "无人机C号-电台C1",
        sourcePlatformId: "31",
        sourcePlatformModel: "1",
        sourcePlatformName: "无人机C号",
        startFreq: 1300000000,
        targetEquipmentId: "6",
        targetEquipmentName: "电台C2",
        targetPlatformId: "50",
        targetPlatformModel: "3",
        targetPlatformName: "测控车",
      },
    ],
    name: "无人机C号-电台C1-电台C2-下行",
    num: 3,
  },
  {
    freqUseDeatils: [
      {
        bandWidth: 10000000,
        centerFreq: 1325000000,
        endFreq: 1330000000,
        linkName: "电台A2-无人机A号-电台A1-上行",
        num: 5,
        power: 30,
        sourceEquipmentId: "2",
        sourceEquipmentName: "电台A2",
        sourcePlatformId: "50",
        sourcePlatformModel: "3",
        sourcePlatformName: "测控车",
        startFreq: 1320000000,
        targetEquipmentId: "1",
        targetEquipmentName: "无人机A号-电台A1",
        targetPlatformId: "11",
        targetPlatformModel: "1",
        targetPlatformName: "无人机A号",
      },
    ],
    name: "电台A2-无人机A号-电台A1-上行",
    num: 5,
  },
  {
    freqUseDeatils: [],
    name: "安全备用频段",
    num: 999,
  },
];

const CollaborationRanges = reactive({
  available: [],
  conflict: [],
  reserved: [],
});
const Categories = ref([]);

function handleGanttUpdate(demoData) {
  // ① 解析消息
  const raw = demoData;
  if (!Array.isArray(raw) || raw.length === 0) return;

  /* -----------------------------------------------------------
   * ② 先拿到类别名，初始化结果骨架
   * --------------------------------------------------------- */
  const categories = raw.map((item) => item.name);
  const n = categories.length;
  const result = {
    available: Array.from({ length: n }, () => []),
    conflict: Array.from({ length: n }, () => []),
    reserved: Array.from({ length: n }, () => []),
  };

  /* -----------------------------------------------------------
   * ③ 拆扫描线事件：普通区段→(start,+1)/(end,-1)
   *    “安全备用频段”→直接进 reserved
   * --------------------------------------------------------- */
  const events = []; // { pos, delta:+1/-1, cat }
  raw.forEach((item, idx) => {
    const isReserved = /备用/.test(item.name);
    item.freqUseDeatils?.forEach((d) => {
      const seg = [d.startFreq, d.endFreq]; // Hz
      if (isReserved) {
        result.reserved[idx].push(seg);
      } else {
        events.push({ pos: d.startFreq, delta: +1, cat: idx });
        events.push({ pos: d.endFreq, delta: -1, cat: idx });
      }
    });
  });

  if (events.length) {
    /* ---------------------------------------------------------
     * ④ 扫描线：统计重叠度，填 available / conflict
     * ------------------------------------------------------- */
    events.sort((a, b) => (a.pos === b.pos ? a.delta - b.delta : a.pos - b.pos));

    const curCats = new Set(); // 当前频点被哪些链路覆盖
    let prevPos = null;
    for (const { pos, delta, cat } of events) {
      if (prevPos !== null && pos > prevPos && curCats.size) {
        const seg = [prevPos, pos]; // 频段 [prev,pos)
        if (curCats.size === 1) {
          // 独占
          result.available[[...curCats][0]].push(seg);
        } else {
          // 冲突
          curCats.forEach((c) => result.conflict[c].push(seg));
        }
      }
      // 更新覆盖集合
      delta === +1 ? curCats.add(cat) : curCats.delete(cat);
      prevPos = pos;
    }
  }

  /* -----------------------------------------------------------
   * ⑤ 写回响应式对象（保持引用稳定，适合 Vue3 视图更新）
   * --------------------------------------------------------- */
  // categories
  Categories.value = categories;

  // available/conflict/reserved
  ["available", "conflict", "reserved"].forEach((key) => {
    CollaborationRanges[key].splice(0, CollaborationRanges[key].length, ...result[key]);
  });
}

onMounted(() => {
  handleGanttUpdate(demoData);
  console.log(CollaborationRanges);
  console.log(Categories.value);
});
</script>

<template>
  <!-- 只要 showChart=false，下面整个区域都不挂载 -->
  <div>
    <template v-for="chart in chartList" :key="chart.id">
      <SpecChart
        :categories="chart.categories"
        :available-ranges="chart.ranges.available"
        :disabled-ranges="chart.ranges.disabled"
        :conflict-ranges="chart.ranges.conflict"
        :reserved-ranges="chart.ranges.reserved"
        :protected-ranges="chart.ranges.protected"
        :x-axis-min="xAxisMin"
        :x-axis-max="xAxisMax"
        :legend-data="['可用频率', '冲突频率', '预留频率', '空闲频率']"
        :show-legend="chart.showLegend"
        :show-YAxis-label="chart.showYAxisLabel"
      />
    </template>
  </div>
</template>
