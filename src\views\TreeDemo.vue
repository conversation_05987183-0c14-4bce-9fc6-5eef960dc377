<template>
  <div class="editable-tree-container" @click="hideContextMenu">
    <el-tree
      node-key="id"
      highlight-current
      draggable
      :data="data"
      :props="defaultProps"
      :render-content="renderContent"
      @node-click="hideContextMenu"
      @node-contextmenu="onNodeContextmenu"
    />
    <!-- 右键菜单 -->
    <div v-if="showMenu" class="context-menu" @click.stop :style="{ top: menuY + 'px', left: menuX + 'px' }">
      <ul>
        <li @click.stop="startEdit(currentNodeData)">编辑</li>
        <!-- 添加编辑功能选项 -->
        <li @click.stop="deleteNode(currentNodeData, getParentNodeData(data, currentNodeData.id))">删除</li>
        <!-- 添加删除功能选项 -->
        <li @click.stop="addChildNode">新增子节点</li>
        <li @click.stop="addNodeBefore">在该节点前新增节点</li>
        <li @click.stop="addNodeAfter">在该节点后新增节点</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, h, nextTick } from "vue";
import { ElTree, ElInput, ElButton, ElMessageBox, ElMessage } from "element-plus";
import "element-plus/dist/index.css";
import { treeData } from "@/utils/treeData.js";

const data = ref(
  treeData.map((node) => ({
    ...node,
    isEditing: false, // 是否处于编辑状态（初始状态下不在编辑模式）
    editLabel: node.label, // 编辑时的临时 label（用于编辑时咱村的新名称）
    // 如果需要深层子节点也有该属性，可调用递归函数，此处略。
  }))
);

const defaultProps = {
  children: "children",
  label: "label",
};

// 准备一个用于右键菜单位置和目标节点信息的状态
const showMenu = ref(false);
const menuX = ref(0); // 菜单距离浏览器左边的位置
const menuY = ref(0); // 菜单距离浏览器上边的位置
const currentEditingNode = ref(null);

// 记录当前右键点击的节点信息
let currentNodeData = null; // 当前节点的数据对象
let currentParentData = null; // 父节点数据对象 (根节点没有父节点)
let currentNodeIndex = -1; // 节点在父children数组中的下标

// 创建一个唯一ID的函数, 实际可用更复杂的ID生成
let nodeIdCounter = 1000;
const generateId = () => {
  return ++nodeIdCounter;
};

// 显示右键菜单
const onNodeContextmenu = (event, treeNode, treeNodeData) => {
  event.preventDefault();
  console.log(treeNode);

  currentNodeData = treeNode; // 当前节点数据
  console.log("当前节点数据", currentNodeData);

  // 找到当前节点的父节点数据和本节点下标
  currentParentData = getParentNodeData(data.value, treeNode.id);
  console.log("当前节点父节点数据", currentParentData);

  if (currentParentData) {
    currentNodeIndex = currentParentData.children.findIndex((child) => child.id === treeNode.id);
  } else {
    // 根节点情况：直接在 data 中找到下标
    currentNodeIndex = data.value.findIndex((d) => d.id === treeNode.id);
  }
  showMenu.value = !showMenu.value;
  menuX.value = event.clientX;
  menuY.value = event.clientY;
};

const hideContextMenu = () => {
  // 点击空白区域隐藏右键菜单
  showMenu.value = false;
};

/**
 * * 新增子节点
 */
const addChildNode = () => {
  if (!currentNodeData) return;
  // 判断 children 是否存在，不存在才创建空数组
  // 如果已经是数组，则直接使用
  if (!Array.isArray(currentNodeData.children)) {
    currentNodeData.children = [];
  }
  currentNodeData.children.push({
    id: generateId(),
    label: "新子节点",
    isEditing: false,
    editLabel: "新子节点",
    children: [],
  });
  hideContextMenu();
};

// 在该节点前新增节点
const addNodeBefore = () => {
  if (currentParentData) {
    currentParentData.children.splice(currentNodeIndex, 0, {
      id: generateId(),
      label: "新节点（前）",
      isEditing: false,
      editLabel: "新节点（前）",
      children: [],
    });
  } else {
    // 根节点处理
    data.value.splice(currentNodeIndex, 0, {
      id: generateId(),
      label: "新节点（前）",
      isEditing: false,
      editLabel: "新节点（前）",
      children: [],
    });
  }
  const originalData = data.value.map((node) => toOriginalStructure(node));
  console.log(originalData, "恢复原始数据结构");
  hideContextMenu();
};

// 在该节点后新增节点
const addNodeAfter = () => {
  if (currentParentData) {
    currentParentData.children.splice(currentNodeIndex + 1, 0, {
      id: generateId(),
      label: "新节点（后）",
      isEditing: false,
      editLabel: "新节点（后）",
      children: [],
    });
  } else {
    // 根节点处理
    data.value.splice(currentNodeIndex + 1, 0, {
      id: generateId(),
      label: "新节点（后）",
      isEditing: false,
      editLabel: "新节点（后）",
      children: [],
    });
  }
  const originalData = data.value.map((node) => toOriginalStructure(node));
  console.log(originalData, "恢复原始数据结构");
  hideContextMenu();
};

// 获取父节点数据的辅助函数
const getParentNodeData = (nodes, id) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.children && node.children.length) {
      // 若在其children中找到
      const index = node.children.findIndex((child) => child.id === id);
      if (index !== -1) {
        return node;
      }
      // 否则继续深入寻找
      const parent = getParentNodeData(node.children, id);
      if (parent) return parent;
    }
  }
  return null; // 未找到父节点(根节点)
};

// 开始编辑某个节点
const startEdit = (nodeData) => {
  if (currentEditingNode.value) {
    // 如果有正在编辑的节点，取消其编辑状态
    cancelEdit(currentEditingNode.value);
  }
  nodeData.isEditing = true;
  nodeData.editLabel = nodeData.label;
  showMenu.value = false;
  currentEditingNode.value = nodeData;
};

// 保存编辑结果
const saveEdit = (nodeData) => {
  if (!nodeData.editLabel.trim()) {
    ElMessage.error("名称不能为空！");
    return;
  }
  nodeData.label = nodeData.editLabel;
  nodeData.isEditing = false;
  ElMessage.success("修改成功");
  currentEditingNode.value = null;
};

// 取消编辑
const cancelEdit = (nodeData) => {
  nodeData.editLabel = nodeData.label;
  nodeData.isEditing = false;
  currentEditingNode.value = null;
};

// 删除节点
const deleteNode = (nodeData, parentData) => {
  if (!parentData) {
    // 尝试从整个data中查找父节点（作为兜底逻辑，以防之前没获取到准确父节点）
    parentData = getParentNodeData(data.value, nodeData.id);
  }
  ElMessageBox.confirm("确定删除此节点吗？", "警告", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      if (parentData) {
        const index = parentData.children.findIndex((child) => child.id === nodeData.id);
        if (index !== -1) {
          parentData.children.splice(index, 1);
          ElMessage.success("删除成功");
        }
      } else {
        // 删除根节点情况（可能已经不存在父节点）
        const rootIndex = data.value.findIndex((d) => d.id === nodeData.id);
        if (rootIndex !== -1) {
          data.value.splice(rootIndex, 1);
          ElMessage.success("删除成功");
        }
      }
    })
    .finally(() => {
      showMenu.value = false;
    });
};

// 假设你的最终需要的数据格式与初始的 treeData 格式相同：
// { id: number, label: string, children: [] }

const toOriginalStructure = (node) => {
  // 从 node 中抽取 id, label, children
  const { id, label, children } = node;
  // 创建一个新的对象，只包含原始结构需要的属性
  const result = { id, label };
  // 如果有 children 且是数组，则递归转换每个子节点
  if (Array.isArray(children)) {
    result.children = children.map((child) => toOriginalStructure(child));
  } else {
    // 确保 children 至少是个空数组
    result.children = [];
  }
  return result;
};

// 自定义渲染节点内容
const renderContent = (hRender, { node, data: nodeData, parent }) => {
  const isEditing = nodeData.isEditing;

  if (isEditing) {
    // 编辑状态下显示输入框和保存/取消按钮
    return h("span", { onClick: (event) => event.stopPropagation() }, [
      h(ElInput, {
        modelValue: nodeData.editLabel,
        size: "small",
        "onUpdate:modelValue": (val) => (nodeData.editLabel = val),
      }),
      h(
        ElButton,
        {
          type: "primary",
          size: "small",
          onClick: (event) => {
            event.stopPropagation(); // 再次确保不冒泡
            saveEdit(nodeData);
          },
        },
        { default: () => "保存" }
      ),
      h(
        ElButton,
        {
          size: "small",
          onClick: (event) => {
            event.stopPropagation();
            cancelEdit(nodeData);
          },
        },
        { default: () => "取消" }
      ),
    ]);
  } else {
    // 非编辑状态下，显示 label
    return h("span", [h("span", { class: "node-label" }, nodeData.label)]);
  }
};
</script>

<style scoped>
.editable-tree-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  padding: 16px;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ccc;
  z-index: 999;
  min-width: 160px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}
.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 4px 0;
}
.context-menu li {
  font-size: 14px;
  font-weight: 400;
  padding: 4px 12px;
  cursor: pointer;
  white-space: nowrap;
}
.context-menu li:hover {
  background: #f5f5f5;
}

:deep(.el-input) {
  width: auto;
  height: 25px;
  margin-right: 10px;
}
</style>
