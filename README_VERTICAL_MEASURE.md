# 垂直测距功能说明

## 功能概述

在原有的水平测距功能基础上，新增了垂直测距功能，支持测量两点之间的高度差。

### 🎯 功能特性

**水平测距**
- ✨ **多点测距** - 支持多点连续测距
- 📏 **实时预览** - 鼠标移动时显示橡皮筋预览
- 🏷️ **距离标签** - 显示每段距离和累计距离
- 🗑️ **单独清除** - 每条测距线都有独立的清除按钮

**垂直测距**
- 📐 **高度差测量** - 测量两点之间的垂直高度差
- 🎯 **简单操作** - 点击两个点即可完成测量
- 🏔️ **高程显示** - 显示精确的高度差值（米）
- 🔵 **青色标识** - 使用青色点和标签区分垂直测距

### 🎮 操作方式

**水平测距操作**：
1. 点击"开始水平测距"按钮
2. 在地图上单击添加测距点
3. 继续单击添加更多点
4. 双击或右键结束测距
5. 点击测距线末端的清除按钮删除

**垂直测距操作**：
1. 点击"开始垂直测距"按钮
2. 在地图上点击第一个点
3. 点击第二个点，自动显示高度差
4. 测距自动结束

### 🛠️ 技术实现

**水平测距核心代码**：
```javascript
// 计算3D空间距离
const len = Cartesian3.distance(p0, p1)

// 累计总距离
const calcTotal = arr => arr.reduce((s, _, i) => 
  i ? s + Cartesian3.distance(arr[i - 1], arr[i]) : 0, 0)
```

**垂直测距核心代码**：
```javascript
// 转换为地理坐标
const c0 = Cartographic.fromCartesian(point1)
const c1 = Cartographic.fromCartesian(point2)

// 计算高度差
const heightDiff = c1.height - c0.height

// 显示格式
const label = `Δh: ${heightDiff.toFixed(1)} m`
```

### 🎨 界面设计

**工具栏布局**：
- 位置：右上角浮动工具栏
- 样式：半透明白色背景，圆角阴影
- 按钮：不同颜色区分功能状态

**按钮状态**：
- 🔵 **蓝色** - 水平测距（未激活）
- 🔴 **红色** - 水平测距（激活中）
- 🟢 **绿色** - 垂直测距（未激活）
- 🔴 **红色** - 垂直测距（激活中）
- 🟠 **橙色** - 清除所有测距

### 📊 显示效果

**水平测距显示**：
- 🔴 红色点标记测距点
- 🟠 橙色折线连接各点
- 🏷️ 白色标签显示距离
- 🟡 黄色虚线预览下一段

**垂直测距显示**：
- 🔵 青色点标记测量点
- 🏷️ 青色标签显示高度差
- 📐 格式：`Δh: ±XX.X m`

### 🔧 功能优化

**防重复触发**：
- 使用延时机制区分单击和双击
- 防止双击时重复添加点
- 状态标志防止重复结束会话

**资源管理**：
- 正确清理事件处理器
- 统一管理所有测距实体
- 组件卸载时自动清理

**用户体验**：
- 鼠标样式变化指示当前模式
- 实时预览提供即时反馈
- 清晰的视觉区分不同测距类型

### 🚀 使用场景

**水平测距适用于**：
- 🏗️ 建筑物间距测量
- 🛣️ 道路长度测量
- 📏 地面距离测量
- 🗺️ 路径规划

**垂直测距适用于**：
- 🏔️ 地形高度差测量
- 🏢 建筑物高度测量
- ✈️ 飞行高度差计算
- 🌊 水位差测量

### 💡 使用技巧

1. **精确测量** - 在3D视图中调整视角获得更精确的点击位置
2. **地形贴合** - 测距点会自动贴合地形表面
3. **多次测量** - 可以同时进行多条水平测距
4. **快速清除** - 使用"清除所有测距"按钮快速清理

这个垂直测距功能完美补充了原有的水平测距，为用户提供了完整的3D空间测量解决方案！
