<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import {
  PolylineCollection,
  Polyline,
  Color,
  Cartesian3,
  Transforms,
  EllipsoidTerrainProvider,
  UrlTemplateImageryProvider,
  MaterialAppearance,
  Viewer,
} from "cesium";
import { radarDetectionArea } from "@/utils/radarPower.js";

const mapContainer = ref(null);
let viewer = null;

function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";

  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );
}

// 计算所有点，z 高度固定为 0
function compute2DPoints(radarLon, radarLat, R_max, nRow, nCol, azimuthOffsetDeg = 0) {
  // 中心点（高度设为 0）
  const center = Cesium.Cartesian3.fromDegrees(radarLon, radarLat, 0);
  const enuMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(center);

  const grid2D = Array(nRow);
  for (let i = 0; i < nRow; i++) {
    grid2D[i] = Array(nCol);
    // 仰角从 0 到 π，控制“环”的半径变化
    const eleRad = (i / (nRow - 1)) * Math.PI;
    const cosE = Math.cos(eleRad);
    const sinE = Math.sin(eleRad);

    for (let j = 0; j < nCol; j++) {
      // 方位从 0 到 2π，再加上偏移
      const azRad = (j / (nCol - 1)) * 2 * Math.PI + Cesium.Math.toRadians(azimuthOffsetDeg);
      const range = R_max[i][j];

      // ENU 平面坐标 (z=0)
      const local = new Cesium.Cartesian3(range * cosE * Math.sin(azRad), range * cosE * Math.cos(azRad), 0);

      const world = new Cesium.Cartesian3();
      Cesium.Matrix4.multiplyByPoint(enuMatrix, local, world);
      grid2D[i][j] = world;
    }
  }

  return grid2D;
}

// 在 2D 图上画出“等仰角环” + “径向线”
function draw2DRadarCoverage({ radarLon, radarLat, R_max, nRow, nCol, azimuthOffsetDeg = 0 }) {
  // 清空旧图形
  viewer.scene.primitives.removeAll();

  // 1) 生成坐标网格
  const grid2D = compute2DPoints(radarLon, radarLat, R_max, nRow, nCol, azimuthOffsetDeg);

  // 2) 半透明黄色线材质
  const appearance = new Cesium.PolylineMaterialAppearance({
    material: Cesium.Material.fromType("Color", {
      color: Cesium.Color.YELLOW.withAlpha(0.4),
    }),
  });

  // 3) 画“等仰角环”——每行闭合
  for (let i = 0; i < nRow; i++) {
    const row = grid2D[i];
    const closed = row.concat(row[0]);
    const geom = new Cesium.PolylineGeometry({
      positions: closed,
      width: 1,
    });
    viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({ geometry: geom }),
        appearance,
        asynchronous: false,
      })
    );
  }

  // 4) 画“径向线”——每列从中心连到外缘
  for (let j = 0; j < nCol; j++) {
    const pts = grid2D.map((row) => row[j]);
    const geom = new Cesium.PolylineGeometry({
      positions: pts,
      width: 1,
    });
    viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({ geometry: geom }),
        appearance,
        asynchronous: false,
      })
    );
  }

  // 5) 把视图缩放到覆盖范围
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(radarLon, radarLat, Math.max(...R_max.flat()) * 1.2),
    orientation: { heading: 0, pitch: -Cesium.Math.PI_OVER_TWO, roll: 0 },
  });
}

// 在 onMounted 中调用
onMounted(() => {
  initMap();
  const { nRow, nCol, R_max } = radarDetectionArea.data;
  draw2DRadarCoverage({
    radarLon: 118.8,
    radarLat: 32.05,
    radarHeight: 50,
    R_max,
    nRow,
    nCol,
  });
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(118.8, 32.05, 50000),
    orientation: { heading: 0, pitch: -Math.PI / 4, roll: 0 },
  });
});

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
});
</script>
