<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import {
  EllipsoidTerrainProvider,
  UrlTemplateImageryProvider,
  Cartesian3,
  Color,
  Rectangle,
  Transforms,
  Matrix4,
} from "cesium";
import { radarDetectionArea } from "@/utils/radarPower.js";

// —— Cesium 初始化 ——
const mapContainer = ref(null);
let viewer = null;

function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webrd0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}",
      subdomains: ["1", "2", "3", "4"],
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees((124.8886 + 131.1114) / 2, (33.482 + 38.518) / 2, 500000),
    orientation: { heading: 0, pitch: -Math.PI / 2, roll: 0 },
  });
  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );
}

function getPointIn3D(radarCartesian, radarLon, radarLat, range, azimuth, elevation) {
  const azRad = Cesium.Math.toRadians(azimuth);
  const elRad = Cesium.Math.toRadians(elevation);

  // 计算雷达在局部坐标系中的x、y、z坐标
  const xLocal = range * Math.cos(elRad) * Math.sin(azRad);
  const yLocal = range * Math.cos(elRad) * Math.cos(azRad);
  const zLocal = range * Math.sin(elRad);

  const localPoint = new Cesium.Cartesian3(xLocal, yLocal, zLocal);

  // 获取从雷达局部ENU坐标系到 ECF 坐标系的变换矩阵
  const enuToFixed = Cesium.Transforms.eastNorthUpToFixedFrame(radarCartesian);

  // 将局部点变换到 ECF 坐标系
  const pointEcef = Cesium.Matrix4.multiplyByPoint(enuToFixed, localPoint, new Cesium.Cartesian3());

  return pointEcef;
}

function draw3DRadarCoverage({ radarLon, radarLat, radarHeight, R_max, nRow, nCol }) {
  // 计算雷达中心
  const radarCartesian = Cesium.Cartesian3.fromDegrees(radarLon, radarLat, radarHeight || 0);

  // 缓存所有点的笛卡尔坐标
  const surfacePoints = Array.from({ length: nRow }, () => Array(nCol));
  for (let i = 0; i < nRow; i++) {
    const elevationAngle = (i / (nRow - 1)) * 30; // 仰角范围0-30度
    for (let j = 0; j < nCol; j++) {
      const azimuth = j * (360 / nCol);
      const range = R_max[i][j];
      surfacePoints[i][j] = getPointIn3D(radarCartesian, radarLon, radarLat, range, azimuth, elevationAngle);
    }
  }

  // 清理
  viewer.scene.primitives.removeAll();

  // 创建所有小面片
  const polygons = [];
  for (let i = 0; i < nRow - 1; i++) {
    for (let j = 0; j < nCol; j++) {
      const nextJ = (j + 1) % nCol; // 闭合
      // 4个点围成一个“小面片”
      const p1 = surfacePoints[i][j];
      const p2 = surfacePoints[i + 1][j];
      const p3 = surfacePoints[i + 1][nextJ];
      const p4 = surfacePoints[i][nextJ];
      polygons.push(
        new Cesium.GeometryInstance({
          geometry: new Cesium.PolygonGeometry({
            polygonHierarchy: new Cesium.PolygonHierarchy([p1, p2, p3, p4]),
            perPositionHeight: true,
          }),
        })
      );
    }
  }

  // 全部面一次性丢进primitive
  viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: polygons,
      appearance: new Cesium.MaterialAppearance({
        material: Cesium.Material.fromType("Color", {
          color: Cesium.Color.YELLOW.withAlpha(0.25),
        }),
        faceForward: true,
        translucent: true,
      }),
      asynchronous: false,
    })
  );
}

onMounted(() => {
  initMap();
  // 示例雷达数据
  const radarLon = 118.8; // 经度
  const radarLat = 32.05; // 纬度
  const radarHeight = 50; // 雷达高度

  // 假设每个点的最大探测距离为100km（可以根据实际数据更改）
  const { nRow, nCol, R_max } = radarDetectionArea.data;

  // 调用绘制三维雷达覆盖函数
  draw3DRadarCoverage({
    radarLon,
    radarLat,
    radarHeight,
    R_max,
    nRow,
    nCol,
  });

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(radarLon, radarLat, 2000), // 调整距离
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-90), // 俯视角度
      roll: 0,
    },
  });
});

onBeforeUnmount(() => {
  viewer && viewer.destroy();
});
</script>

<style scoped>
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
