<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import {
  EllipsoidTerrainProvider,
  UrlTemplateImageryProvider,
  Cartesian3,
  Color,
  GeometryInstance,
  RectangleGeometry,
  Primitive,
  ColorGeometryInstanceAttribute,
  PerInstanceColorAppearance,
  Rectangle,
} from "cesium";
import { commCapData } from "@/utils/radarPower.js";

const mapContainer = ref(null);
let viewer;

function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webrd0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}",
      subdomains: ["1", "2", "3", "4"],
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees((124.8886 + 131.1114) / 2, (33.482 + 38.518) / 2, 500000),
    orientation: { heading: 0, pitch: -Math.PI / 2, roll: 0 },
  });
  // 覆盖底图
  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) {
    layers.remove(layers.get(i));
  }
  layers.addImageryProvider(
    new UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );
  layers.addImageryProvider(
    new UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8,
    })
  );
}

/**
 * 把功率矩阵画成立方柱体，height=0→extrudedHeight=h
 * @param {{ nRow:number, nCol:number, lonlim:number[], latlim:number[], pEr:number[][] }} data
 */
function draw3DCoverage(data) {
  const { nRow, nCol, lonlim, latlim, pEr } = data;
  // 归一化找 min/max
  const flat = pEr.flat();
  const vMin = Math.min(...flat);
  const vMax = Math.max(...flat);
  // 定义高度范围，按实际场景调整
  const minH = 0;
  const maxH = 50000;

  const instances = [];
  for (let i = 0; i < nRow; i++) {
    // 纬度区间
    const lat0 = latlim[0] + (latlim[1] - latlim[0]) * (i / nRow);
    const lat1 = latlim[0] + (latlim[1] - latlim[0]) * ((i + 1) / nRow);
    for (let j = 0; j < nCol; j++) {
      const lon0 = lonlim[0] + (lonlim[1] - lonlim[0]) * (j / nCol);
      const lon1 = lonlim[0] + (lonlim[1] - lonlim[0]) * ((j + 1) / nCol);
      // 计算高度
      const t = (pEr[i][j] - vMin) / (vMax - vMin);
      const h = minH + (maxH - minH) * t;
      // 颜色同热力：蓝(0.66)→红(0)
      const hue = (1 - t) * 0.66;
      const col = Color.fromHsl(hue, 1, 0.5).withAlpha(0.8);

      // 构造每个网格的 GeometryInstance
      instances.push(
        new GeometryInstance({
          geometry: new RectangleGeometry({
            rectangle: Rectangle.fromDegrees(lon0, lat0, lon1, lat1),
            height: 0,
            extrudedHeight: h,
            perPositionHeight: true,
          }),
          attributes: {
            color: ColorGeometryInstanceAttribute.fromColor(col),
          },
        })
      );
    }
  }

  viewer.scene.primitives.add(
    new Primitive({
      asynchronous: false,
      geometryInstances: instances,
      appearance: new PerInstanceColorAppearance({
        closed: true,
        translucent: true,
      }),
    })
  );
}

onMounted(async () => {
  initMap();
  // 直接用本地测试数据
  const { data } = commCapData;
  draw3DCoverage(data);
});

onBeforeUnmount(() => {
  viewer && viewer.destroy();
});
</script>

<style scoped>
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
