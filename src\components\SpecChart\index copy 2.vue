<!-- FrequencyChart.vue -->
<template>
  <div ref="chartRef" class="freq-chart" />
</template>

<script setup>
  import * as echarts from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components'
  import { CustomChart, BarChart } from 'echarts/charts'

  // 注册 ECharts 模块
  echarts.use([
    CanvasRenderer,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    CustomChart,
    BarChart
  ])

  const props = defineProps({
    categories: {
      type: Array,
      default: () => []
    },
    availableRanges: {
      type: Array,
      default: () => []
    },
    disabledRanges: {
      type: Array,
      default: () => []
    },
    conflictRanges: {
      type: Array,
      default: () => []
    },
    reservedRanges: {
      type: Array,
      default: () => []
    },
    protectedRanges: {
      type: Array,
      default: () => []
    },
    xAxisMin: {
      type: Number,
      default: 30
    },
    xAxisMax: {
      type: Number,
      default: 18000
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    legendData: {
      type: Array,
      default: () => ['可用频率', '禁用频率', '冲突频率', '预留频率', '保护频率']
    },
    showYAxisLabel: {
      type: Boolean,
      default: true
    }
  })

  const chartRef = ref(null)
  let chartInstance = null

  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    window.removeEventListener('resize', resizeChart)
  })

  function makeOption(categories, available, disabled, conflict, reserved, protect) {
    const colorMap = {
      可用频率: '#b0cf95',
      禁用频率: '#b95f29',
      冲突频率: '#ea3423',
      预留频率: '#385391',
      保护频率: '#f5c242'
    }

    // 准备所有频段数据
    const rangeMap = {
      可用频率: available,
      禁用频率: disabled,
      冲突频率: conflict,
      预留频率: reserved,
      保护频率: protect
    }

    // 生成系列数据
    const seriesData = props.legendData.map(name => {
      const color = colorMap[name]

      return {
        name,
        type: 'custom',
        renderItem: (params, api) => {
          const categoryIndex = api.value(0)
          const ranges = api.value(1)

          // 确保 ranges 是一个数组
          if (!Array.isArray(ranges) || ranges.length === 0) return

          const height = api.size([0, 1])[1] * 1
          const y0 = api.coord([0, categoryIndex])[1] - height / 2
          const y1 = y0 + height

          // 检查 ranges 中的每个元素是否是有效的频段
          const validRanges = ranges.filter(
            range =>
              Array.isArray(range) && range.length === 2 && !isNaN(range[0]) && !isNaN(range[1])
          )

          // 渲染多个不连续的矩形
          const children = validRanges.map(range => {
            const [start, end] = range
            const x0 = api.coord([start, categoryIndex])[0]
            const x1 = api.coord([end, categoryIndex])[0]

            return {
              type: 'rect',
              shape: {
                x: x0,
                y: y0,
                width: x1 - x0,
                height: height
              },
              style: {
                fill: color
              }
            }
          })

          return {
            type: 'group',
            children: children
          }
        },
        data: categories.map((_, idx) => {
          // 确保每个类别的数据是一个数组的数组
          const ranges = rangeMap[name][idx] || []
          return [idx, Array.isArray(ranges[0]) ? ranges : [ranges]]
        })
      }
    })

    const colors = props.legendData.map(name => colorMap[name] || '#ccc')

    return {
      color: colors,
      tooltip: {
        trigger: 'item',
        formatter: params => {
          const seriesName = params.seriesName
          const data = params.data[1] // 获取频段数组

          if (!Array.isArray(data) || data.length === 0) return ''

          // 将频段数组格式化为字符串
          const rangeText = data
            .map(range => {
              if (!Array.isArray(range) || range.length !== 2) return ''
              const [start, end] = range
              const fmt = v => (v >= 1000 ? (v / 1000).toFixed(2) + 'GHz' : v.toFixed(0) + 'MHz')
              return `${fmt(start)} - ${fmt(end)}`
            })
            .filter(Boolean)
            .join('<br/>')

          return `${seriesName}<br/>${rangeText}`
        }
      },
      legend: {
        show: props.showLegend,
        data: props.legendData,
        bottom: 0,
        itemGap: 12
      },
      grid: { left: 100, right: 40, top: 40, bottom: 60, containLabel: true },
      xAxis: {
        type: 'value',
        min: props.xAxisMin / 1e6,
        max: props.xAxisMax / 1e6,
        splitLine: { show: true },
        axisLabel: {
          formatter: v => {
            if (v >= 1000) return (v / 1000).toFixed(0) + 'GHz'
            return v.toFixed(0) + 'MHz'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisTick: { show: false },
        axisLine: { show: false },
        splitLine: { show: true },
        axisLabel: {
          show: props.showYAxisLabel,
          color: '#666'
        }
      },
      series: seriesData
    }
  }

  const renderChart = () => {
    if (!chartRef.value) return

    // 转换单位到MHz并确保数据结构正确
    const processRanges = arr => {
      return arr.map(item => {
        if (!Array.isArray(item)) return []
        // 如果是一维数组 [start, end]，转换为二维数组 [[start, end]]
        if (item.length === 2 && !Array.isArray(item[0])) {
          return [[item[0] / 1e6, item[1] / 1e6]]
        }
        // 如果是二维数组，转换每个频段
        return item.map(([s, e]) => [s / 1e6, e / 1e6])
      })
    }

    nextTick(() => {
      if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value)
        window.addEventListener('resize', resizeChart)
      }

      const cat = props.categories
      const av = processRanges(props.availableRanges)
      const dis = processRanges(props.disabledRanges)
      const cof = processRanges(props.conflictRanges)
      const res = processRanges(props.reservedRanges)
      const pro = processRanges(props.protectedRanges)

      chartInstance.setOption(makeOption(cat, av, dis, cof, res, pro))
    })
  }

  function resizeChart() {
    chartInstance?.resize()
  }

  watch(() => props.categories, renderChart, { deep: true })
  watch(() => props.availableRanges, renderChart, { deep: true })
  watch(() => props.disabledRanges, renderChart, { deep: true })
  watch(() => props.conflictRanges, renderChart, { deep: true })
  watch(() => props.reservedRanges, renderChart, { deep: true })
  watch(() => props.protectedRanges, renderChart, { deep: true })
  watch(() => props.legendData, renderChart, { deep: true })

  onMounted()

  defineExpose({
    renderChart
  })
</script>

<style scoped>
  .freq-chart {
    width: 100%;
    height: 360px;
  }
</style>
