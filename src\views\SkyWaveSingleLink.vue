<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

/** 1️⃣ 画面元素的 ref */
const videoRef = ref(null);

/** 2️⃣ 后端两个 WS 地址：
 *   - controlUrl: 用来下发“开始推流”之类的命令
 *   - streamUrl:  flv.js 自动连接并拉取的流地址
 */
const controlUrl = "ws://your.server:9999/control";
const streamUrl = "ws://your.server:9999/live.flv";

/** 3️⃣ 存放控制 WS 和 flv.js 播放器实例 */
let controlWs = null;
let player = null;

/** 4️⃣ 点击按钮时调用：通知后端开始推流，然后启动播放器 */
function requestStream() {
  if (!controlWs || controlWs.readyState !== WebSocket.OPEN) {
    console.error("控制 WebSocket 尚未连接");
    return;
  }

  // ① 通过 controlWs 发送一条启动流命令
  controlWs.send(JSON.stringify({ action: "start_stream" }));

  // ② 用 flv.js 创建播放器并开始播放
  if (flvjs.isSupported()) {
    player = flvjs.createPlayer({
      type: "flv",
      url: streamUrl,
      isLive: true,
      cors: true,
    });
    player.attachMediaElement(videoRef.value);
    player.load();
    player.play().catch((err) => {
      console.error("FLV 播放启动失败", err);
    });
  } else {
    console.error("浏览器不支持 flv.js");
  }
}

onMounted(() => {
  // 建立控制 WebSocket 连接
  controlWs = new WebSocket(controlUrl);
  controlWs.onopen = () => console.log("控制 WS 已连接");
  controlWs.onmessage = (evt) => console.log("收到后端消息：", evt.data);
  controlWs.onerror = (err) => console.error("控制 WS 错误", err);
  controlWs.onclose = () => console.log("控制 WS 已关闭");
});

onBeforeUnmount(() => {
  // 卸载 flv.js 播放器
  if (player) {
    player.unload();
    player.detachMediaElement();
    player.destroy();
    player = null;
  }
  // 关闭控制 WebSocket
  if (controlWs) {
    controlWs.close();
    controlWs = null;
  }
});
</script>

<template>
  <div>
    <!-- 引入新的视频流播放器组件 -->
    <VideoStreamPlayer />
  </div>
</template>

<style scoped>
/* 如需调整按钮或视频的样式，可在这里写 */
button {
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
}
</style>
