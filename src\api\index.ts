import request from "../utils/request";

export const fetchData = () => {
  return request({
    url: "./mock/table.json",
    method: "get",
  });
};

export const fetchUserData = () => {
  return request({
    url: "./mock/user.json",
    method: "get",
  });
};

export const fetchRoleData = () => {
  return request({
    url: "./mock/role.json",
    method: "get",
  });
};

export const fetchGraphData = () => {
  return request({
    url: "./mock/graph.json",
    method: "get",
  });
};
