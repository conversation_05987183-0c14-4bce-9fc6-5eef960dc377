import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import cesium from "vite-plugin-cesium";
import VueSetupExtend from "vite-plugin-vue-setup-extend";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
export default defineConfig({
  base: "./",
  plugins: [
    vue(),
    cesium(),
    VueSetupExtend(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  optimizeDeps: {
    include: ["schart.js"],
  },
  resolve: {
    alias: {
      "@": "/src",
      "~": "/src/assets",
      cesium: "cesium", // Cesium 别名
    },
  },
  build: {
    target: "es2015",
    // 可选：根据需要配置 Rollup 选项
    rollupOptions: {
      output: {
        globals: {
          cesium: "Cesium",
        },
      },
    },
  },
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: "true",
    CESIUM_BASE_URL: JSON.stringify("./cesium/"), // 确保基础 URL 正确指向
  },
});
