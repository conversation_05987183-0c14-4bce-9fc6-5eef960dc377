<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import { EllipsoidTerrainProvider, UrlTemplateImageryProvider, Cartesian3, Color, Rectangle } from "cesium";
import { commCapData } from "@/utils/radarPower.js";

// —— Cesium 初始化 ——
const mapContainer = ref(null);
let viewer;
function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webrd0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}",
      subdomains: ["1", "2", "3", "4"],
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees((124.8886 + 131.1114) / 2, (33.482 + 38.518) / 2, 500000),
    orientation: { heading: 0, pitch: -Math.PI / 2, roll: 0 },
  });
  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );
}

/**
 * 把 pEr 网格渲染到一个小画布上，然后再拉伸到大尺寸以开启平滑插值
 * @param {number[][]} pEr    原始功率矩阵，大小 nRow×nCol
 * @param {number}    nRow
 * @param {number}    nCol
 * @param {number}    smoothSize 最终画布边长（px），建议512或1024
 */
function makeHeatCanvas(pEr, nRow, nCol, smoothSize = 512) {
  // 1) 先在一个 nCol×nRow 的小画布上，逐像素填色
  const small = document.createElement("canvas");
  small.width = nCol;
  small.height = nRow;
  const sCtx = small.getContext("2d");

  // 归一化找 min/max
  const flat = pEr.flat();
  const vMin = Math.min(...flat);
  const vMax = Math.max(...flat);

  for (let i = 0; i < nRow; i++) {
    for (let j = 0; j < nCol; j++) {
      const t = (pEr[i][j] - vMin) / (vMax - vMin);
      const hue = (1 - t) * 0.66; // HSL 0.66→0 (蓝→红)
      const col = Cesium.Color.fromHsl(hue, 1, 0.5).withAlpha(0.6).toCssColorString();
      sCtx.fillStyle = col;
      sCtx.fillRect(j, i, 1, 1);
    }
  }

  // 2) 把小画布“平滑拉伸”到 smoothSize×smoothSize
  const big = document.createElement("canvas");
  big.width = smoothSize;
  big.height = smoothSize;
  const bCtx = big.getContext("2d");
  bCtx.imageSmoothingEnabled = true;
  bCtx.imageSmoothingQuality = "high";
  // 一次性插值到大尺寸
  bCtx.drawImage(small, 0, 0, smoothSize, smoothSize);

  return big;
}

/**
 * 用 CanvasMaterialProperty 在地图上画一个圆形的贴图热力图
 * @param {*} opts
 * @param {number} opts.lon      圆心经度
 * @param {number} opts.lat      圆心纬度
 * @param {number} opts.radius   圆半径（米）
 * @param {number[][]} opts.pEr  功率矩阵
 * @param {number}    opts.nRow
 * @param {number}    opts.nCol
 */
function drawSmoothHeatCircle({ lon, lat, radius, pEr, nRow, nCol }) {
  // 生成平滑后的 Canvas
  const canvas = makeHeatCanvas(pEr, nRow, nCol, 512);

  // 用它做 ImageMaterial
  const material = new Cesium.ImageMaterialProperty({
    image: canvas,
    transparent: true,
  });

  // 添加到 Cesium
  viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(lon, lat),
    ellipse: {
      semiMajorAxis: radius,
      semiMinorAxis: radius,
      material: material,
      // height: 0,
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      outline: false,
    },
  });
}

// —— 示范：假设你已经通过 fetch 拿到上面那个 JSON ——
onMounted(async () => {
  initMap();

  // 举例：用本地变量 powerData，实际上请改为你的 fetch 调用
  const powerData = commCapData;
  // 假设你已 fetch 到后端数据为 json.data
  const { nRow, nCol, lonlim, latlim, pEr } = powerData.data;

  // 计算圆心和半径
  const centerLon = (lonlim[0] + lonlim[1]) / 2;
  const centerLat = (latlim[0] + latlim[1]) / 2;
  const c0 = Cesium.Cartographic.fromDegrees(centerLon, centerLat); // 圆心经纬坐标转笛卡尔坐标
  const ce = Cesium.Cartographic.fromDegrees(lonlim[1], centerLat); // 圆心到东边界的笛卡尔距离
  const radius = new Cesium.EllipsoidGeodesic(c0, ce).surfaceDistance; //计算半径

  drawSmoothHeatCircle({
    lon: centerLon,
    lat: centerLat,
    radius,
    pEr,
    nRow,
    nCol,
  });
});

onBeforeUnmount(() => {
  viewer && viewer.destroy();
});
</script>

<style scoped>
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
