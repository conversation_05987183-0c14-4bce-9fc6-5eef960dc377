const express = require('express');
const mysql = require('mysql2/promise');

const app = express();
const port = 3001;

// 配置数据库连接信息
const pool = mysql.createPool({
  host: 'localhost', // 数据库主机地址
  user: 'root', // 数据库用户名
  password: 'Yuzb0428', // 数据库密码
  database: 'jsgaocheng', // 数据库名称
  waitForConnections: true, // 等待连接
  connectionLimit: 10, // 连接池大小
  queueLimit: 0 // 无限制的连接请求队列
});

// 手动设置 CORS 头
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173'); // 允许的源
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE'); // 允许的请求方法
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // 允许的请求头
  next();
});

// 定义 API 接口来查询高程数据
app.get('/api/getElevation', async (req, res) => {
  const lon = req.query.lon;
  const lat = req.query.lat;
  if (!lon || !lat) {
    return res.status(400).json({ error: '经度和纬度参数不能为空' });
  }
  try {
    const query = `
          SELECT elevation
          FROM elevation_data
          ORDER BY location <-> POINT(118.7822, 36.9765)
          LIMIT 1;
      `;
    const [rows] = await pool.execute(query, [lon, lat]);
    if (rows.length > 0) {
      res.json({ elevation: rows[0].elevation });
    } else {
      res.status(404).json({ error: '未找到相关高程数据' });
    }
  } catch (error) {
    console.error('数据库查询出错:', error);
    res.status(500).json({ error: '数据库查询出错' });
  }
});

// 启动服务器
app.listen(port, () => {
  console.log(`服务器运行在端口 ${port}`);
});
