<script setup>
import { ref } from "vue";
import WebSocketFlvPlayer from "./WebSocketFlvPlayer.vue";
import VideoStreamPlayer from "./VideoStreamPlayer.vue";
import BinaryFlvPlayer from "./BinaryFlvPlayer.vue";

const currentPlayer = ref("binary");

const players = [
  { key: "binary", name: "二进制FLV播放器 (推荐)", component: "BinaryFlvPlayer" },
  { key: "websocket", name: "WebSocket FLV播放器", component: "WebSocketFlvPlayer" },
  { key: "basic", name: "基础视频流播放器", component: "VideoStreamPlayer" },
];
</script>

<template>
  <div class="flv-player-test">
    <div class="header">
      <h1>FLV 播放器测试页面</h1>
      <div class="player-selector">
        <label>选择播放器:</label>
        <select v-model="currentPlayer">
          <option v-for="player in players" :key="player.key" :value="player.key">
            {{ player.name }}
          </option>
        </select>
      </div>
    </div>

    <div class="content">
      <div class="instructions">
        <h3>使用说明</h3>
        <ol>
          <li>确保后端 WebSocket 服务器运行在 <code>ws://***********:9085/ws/video-stream</code></li>
          <li>点击"开始推流"按钮发送开始指令</li>
          <li>后端接收到指令后会开始推送 FLV 数据</li>
          <li>前端接收到 messageType: 50 的消息后开始播放</li>
          <li>点击"停止推流"按钮停止播放</li>
        </ol>

        <h4>支持的数据格式</h4>
        <ul>
          <li><strong>二进制格式 (推荐):</strong> 后端直接推送 FLV 二进制数据</li>
          <li><strong>JSON 格式:</strong> <code>{"messageType": 50, "data": "base64或二进制数组"}</code></li>
        </ul>

        <div class="note">
          <strong>注意:</strong> 根据你的描述，后端直接推送二进制数据，建议使用 "二进制FLV播放器" 选项。
        </div>

        <h4>指令格式</h4>
        <div class="code-block">
          <h5>开始指令:</h5>
          <pre><code>{
  "action": "start",
  "streamUrl": "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8"
}</code></pre>

          <h5>停止指令:</h5>
          <pre><code>{
  "action": "stop"
}</code></pre>
        </div>
      </div>

      <div class="player-container">
        <BinaryFlvPlayer v-if="currentPlayer === 'binary'" />
        <WebSocketFlvPlayer v-else-if="currentPlayer === 'websocket'" />
        <VideoStreamPlayer v-else-if="currentPlayer === 'basic'" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.flv-player-test {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
}

.player-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.player-selector label {
  font-weight: 500;
  color: #666;
}

.player-selector select {
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  min-width: 200px;
}

.content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 30px;
}

.instructions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  height: fit-content;
}

.instructions h3 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
}

.instructions h4 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #555;
  font-size: 16px;
}

.instructions h5 {
  margin-top: 15px;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.instructions ol,
.instructions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

.instructions code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: "Courier New", monospace;
  font-size: 13px;
  color: #d63384;
}

.code-block {
  margin-top: 15px;
}

.code-block pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: "Courier New", monospace;
  font-size: 13px;
  line-height: 1.4;
  margin: 8px 0;
}

.code-block code {
  background: none;
  padding: 0;
  color: inherit;
}

.note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 12px;
  margin: 15px 0;
  color: #856404;
}

.player-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .content {
    grid-template-columns: 1fr;
  }

  .instructions {
    order: 2;
  }

  .player-container {
    order: 1;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header h1 {
    font-size: 24px;
  }

  .player-selector {
    width: 100%;
    justify-content: space-between;
  }

  .player-selector select {
    min-width: 150px;
  }
}
</style>
