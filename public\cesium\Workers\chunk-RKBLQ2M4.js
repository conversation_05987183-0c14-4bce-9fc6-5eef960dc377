/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.122
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as _,b as Me,c as Oe,d as ze,e as W,f as ft}from"./chunk-UTRPTI5S.js";import{a as E}from"./chunk-TFNGIACM.js";import{a as Se}from"./chunk-XUAQFAMT.js";import{a as O}from"./chunk-OW23VKVW.js";import{a as A,b as s}from"./chunk-LKAZ42NI.js";import{a as St,c as Rt,d as Ze,e as p}from"./chunk-MYHWD27O.js";var Un=Rt((et,tt)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var t=typeof et=="object"&&et&&!et.nodeType&&et,n=typeof tt=="object"&&tt&&!tt.nodeType&&tt,o=typeof global=="object"&&global;(o.global===o||o.window===o||o.self===o)&&(e=o);var i,r=**********,a=36,u=1,d=26,m=38,l=700,w=72,T=128,v="-",P=/^xn--/,j=/[^\x20-\x7E]/,q=/[\x2E\u3002\uFF0E\uFF61]/g,k={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},x=a-u,F=Math.floor,B=String.fromCharCode,Q;function H(b){throw new RangeError(k[b])}function ee(b,z){for(var N=b.length,Y=[];N--;)Y[N]=z(b[N]);return Y}function re(b,z){var N=b.split("@"),Y="";N.length>1&&(Y=N[0]+"@",b=N[1]),b=b.replace(q,".");var ie=b.split("."),ae=ee(ie,z).join(".");return Y+ae}function Z(b){for(var z=[],N=0,Y=b.length,ie,ae;N<Y;)ie=b.charCodeAt(N++),ie>=55296&&ie<=56319&&N<Y?(ae=b.charCodeAt(N++),(ae&64512)==56320?z.push(((ie&1023)<<10)+(ae&1023)+65536):(z.push(ie),N--)):z.push(ie);return z}function oe(b){return ee(b,function(z){var N="";return z>65535&&(z-=65536,N+=B(z>>>10&1023|55296),z=56320|z&1023),N+=B(z),N}).join("")}function J(b){return b-48<10?b-22:b-65<26?b-65:b-97<26?b-97:a}function c(b,z){return b+22+75*(b<26)-((z!=0)<<5)}function f(b,z,N){var Y=0;for(b=N?F(b/l):b>>1,b+=F(b/z);b>x*d>>1;Y+=a)b=F(b/x);return F(Y+(x+1)*b/(b+m))}function h(b){var z=[],N=b.length,Y,ie=0,ae=T,te=w,ue,de,ye,me,he,X,_e,Te,je;for(ue=b.lastIndexOf(v),ue<0&&(ue=0),de=0;de<ue;++de)b.charCodeAt(de)>=128&&H("not-basic"),z.push(b.charCodeAt(de));for(ye=ue>0?ue+1:0;ye<N;){for(me=ie,he=1,X=a;ye>=N&&H("invalid-input"),_e=J(b.charCodeAt(ye++)),(_e>=a||_e>F((r-ie)/he))&&H("overflow"),ie+=_e*he,Te=X<=te?u:X>=te+d?d:X-te,!(_e<Te);X+=a)je=a-Te,he>F(r/je)&&H("overflow"),he*=je;Y=z.length+1,te=f(ie-me,Y,me==0),F(ie/Y)>r-ae&&H("overflow"),ae+=F(ie/Y),ie%=Y,z.splice(ie++,0,ae)}return oe(z)}function y(b){var z,N,Y,ie,ae,te,ue,de,ye,me,he,X=[],_e,Te,je,at;for(b=Z(b),_e=b.length,z=T,N=0,ae=w,te=0;te<_e;++te)he=b[te],he<128&&X.push(B(he));for(Y=ie=X.length,ie&&X.push(v);Y<_e;){for(ue=r,te=0;te<_e;++te)he=b[te],he>=z&&he<ue&&(ue=he);for(Te=Y+1,ue-z>F((r-N)/Te)&&H("overflow"),N+=(ue-z)*Te,z=ue,te=0;te<_e;++te)if(he=b[te],he<z&&++N>r&&H("overflow"),he==z){for(de=N,ye=a;me=ye<=ae?u:ye>=ae+d?d:ye-ae,!(de<me);ye+=a)at=de-me,je=a-me,X.push(B(c(me+at%je,0))),de=F(at/je);X.push(B(c(de,0))),ae=f(N,Te,Y==ie),N=0,++Y}++N,++z}return X.join("")}function g(b){return re(b,function(z){return P.test(z)?h(z.slice(4).toLowerCase()):z})}function C(b){return re(b,function(z){return j.test(z)?"xn--"+y(z):z})}if(i={version:"1.3.2",ucs2:{decode:Z,encode:oe},decode:h,encode:y,toASCII:C,toUnicode:g},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return i});else if(t&&n)if(tt.exports==t)n.exports=i;else for(Q in i)i.hasOwnProperty(Q)&&(t[Q]=i[Q]);else e.punycode=i})(et)});var zn=Rt((In,Mt)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Mt=="object"&&Mt.exports?Mt.exports=t():typeof define=="function"&&define.amd?define(t):e.IPv6=t(e)})(In,function(e){"use strict";var t=e&&e.IPv6;function n(i){var r=i.toLowerCase(),a=r.split(":"),u=a.length,d=8;a[0]===""&&a[1]===""&&a[2]===""?(a.shift(),a.shift()):a[0]===""&&a[1]===""?a.shift():a[u-1]===""&&a[u-2]===""&&a.pop(),u=a.length,a[u-1].indexOf(".")!==-1&&(d=7);var m;for(m=0;m<u&&a[m]!=="";m++);if(m<d)for(a.splice(m,1,"0000");a.length<d;)a.splice(m,0,"0000");for(var l,w=0;w<d;w++){l=a[w].split("");for(var T=0;T<3&&(l[0]==="0"&&l.length>1);T++)l.splice(0,1);a[w]=l.join("")}var v=-1,P=0,j=0,q=-1,k=!1;for(w=0;w<d;w++)k?a[w]==="0"?j+=1:(k=!1,j>P&&(v=q,P=j)):a[w]==="0"&&(k=!0,q=w,j=1);j>P&&(v=q,P=j),P>1&&a.splice(v,P,""),u=a.length;var x="";for(a[0]===""&&(x=":"),w=0;w<u&&(x+=a[w],w!==u-1);w++)x+=":";return a[u-1]===""&&(x+=":"),x}function o(){return e.IPv6===this&&(e.IPv6=t),this}return{best:n,noConflict:o}})});var Dn=Rt((qn,Pt)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Pt=="object"&&Pt.exports?Pt.exports=t():typeof define=="function"&&define.amd?define(t):e.SecondLevelDomains=t(e)})(qn,function(e){"use strict";var t=e&&e.SecondLevelDomains,n={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return!1;var a=n.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(r+1,i)+" ")>=0:!1},is:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r>=0)return!1;var a=n.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(0,i)+" ")>=0:!1},get:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return null;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return null;var a=n.list[o.slice(i+1)];return!a||a.indexOf(" "+o.slice(r+1,i)+" ")<0?null:o.slice(r+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return n})});var We=Rt((Nn,Ut)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Ut=="object"&&Ut.exports?Ut.exports=t(Un(),zn(),Dn()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],t):e.URI=t(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(Nn,function(e,t,n,o){"use strict";var i=o&&o.URI;function r(c,f){var h=arguments.length>=1,y=arguments.length>=2;if(!(this instanceof r))return h?y?new r(c,f):new r(c):new r;if(c===void 0){if(h)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?c=location.href+"":c=""}if(c===null&&h)throw new TypeError("null is not a valid argument for URI");return this.href(c),f!==void 0?this.absoluteTo(f):this}function a(c){return/^[0-9]+$/.test(c)}r.version="1.19.11";var u=r.prototype,d=Object.prototype.hasOwnProperty;function m(c){return c.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(c){return c===void 0?"Undefined":String(Object.prototype.toString.call(c)).slice(8,-1)}function w(c){return l(c)==="Array"}function T(c,f){var h={},y,g;if(l(f)==="RegExp")h=null;else if(w(f))for(y=0,g=f.length;y<g;y++)h[f[y]]=!0;else h[f]=!0;for(y=0,g=c.length;y<g;y++){var C=h&&h[c[y]]!==void 0||!h&&f.test(c[y]);C&&(c.splice(y,1),g--,y--)}return c}function v(c,f){var h,y;if(w(f)){for(h=0,y=f.length;h<y;h++)if(!v(c,f[h]))return!1;return!0}var g=l(f);for(h=0,y=c.length;h<y;h++)if(g==="RegExp"){if(typeof c[h]=="string"&&c[h].match(f))return!0}else if(c[h]===f)return!0;return!1}function P(c,f){if(!w(c)||!w(f)||c.length!==f.length)return!1;c.sort(),f.sort();for(var h=0,y=c.length;h<y;h++)if(c[h]!==f[h])return!1;return!0}function j(c){var f=/^\/+|\/+$/g;return c.replace(f,"")}r._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:r.preventInvalidHostname,duplicateQueryParameters:r.duplicateQueryParameters,escapeQuerySpace:r.escapeQuerySpace}},r.preventInvalidHostname=!1,r.duplicateQueryParameters=!1,r.escapeQuerySpace=!0,r.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,r.idn_expression=/[^a-z0-9\._-]/i,r.punycode_expression=/(xn--)/i,r.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,r.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,r.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,r.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},r.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,r.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,r.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},r.hostProtocols=["http","https"],r.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,r.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},r.getDomAttribute=function(c){if(!(!c||!c.nodeName)){var f=c.nodeName.toLowerCase();if(!(f==="input"&&c.type!=="image"))return r.domAttributes[f]}};function q(c){return escape(c)}function k(c){return encodeURIComponent(c).replace(/[!'()*]/g,q).replace(/\*/g,"%2A")}r.encode=k,r.decode=decodeURIComponent,r.iso8859=function(){r.encode=escape,r.decode=unescape},r.unicode=function(){r.encode=k,r.decode=decodeURIComponent},r.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},r.encodeQuery=function(c,f){var h=r.encode(c+"");return f===void 0&&(f=r.escapeQuerySpace),f?h.replace(/%20/g,"+"):h},r.decodeQuery=function(c,f){c+="",f===void 0&&(f=r.escapeQuerySpace);try{return r.decode(f?c.replace(/\+/g,"%20"):c)}catch{return c}};var x={encode:"encode",decode:"decode"},F,B=function(c,f){return function(h){try{return r[f](h+"").replace(r.characters[c][f].expression,function(y){return r.characters[c][f].map[y]})}catch{return h}}};for(F in x)r[F+"PathSegment"]=B("pathname",x[F]),r[F+"UrnPathSegment"]=B("urnpath",x[F]);var Q=function(c,f,h){return function(y){var g;h?g=function(N){return r[f](r[h](N))}:g=r[f];for(var C=(y+"").split(c),b=0,z=C.length;b<z;b++)C[b]=g(C[b]);return C.join(c)}};r.decodePath=Q("/","decodePathSegment"),r.decodeUrnPath=Q(":","decodeUrnPathSegment"),r.recodePath=Q("/","encodePathSegment","decode"),r.recodeUrnPath=Q(":","encodeUrnPathSegment","decode"),r.encodeReserved=B("reserved","encode"),r.parse=function(c,f){var h;return f||(f={preventInvalidHostname:r.preventInvalidHostname}),c=c.replace(r.leading_whitespace_expression,""),c=c.replace(r.ascii_tab_whitespace,""),h=c.indexOf("#"),h>-1&&(f.fragment=c.substring(h+1)||null,c=c.substring(0,h)),h=c.indexOf("?"),h>-1&&(f.query=c.substring(h+1)||null,c=c.substring(0,h)),c=c.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),c=c.replace(/^[/\\]{2,}/i,"//"),c.substring(0,2)==="//"?(f.protocol=null,c=c.substring(2),c=r.parseAuthority(c,f)):(h=c.indexOf(":"),h>-1&&(f.protocol=c.substring(0,h)||null,f.protocol&&!f.protocol.match(r.protocol_expression)?f.protocol=void 0:c.substring(h+1,h+3).replace(/\\/g,"/")==="//"?(c=c.substring(h+3),c=r.parseAuthority(c,f)):(c=c.substring(h+1),f.urn=!0))),f.path=c,f},r.parseHost=function(c,f){c||(c=""),c=c.replace(/\\/g,"/");var h=c.indexOf("/"),y,g;if(h===-1&&(h=c.length),c.charAt(0)==="[")y=c.indexOf("]"),f.hostname=c.substring(1,y)||null,f.port=c.substring(y+2,h)||null,f.port==="/"&&(f.port=null);else{var C=c.indexOf(":"),b=c.indexOf("/"),z=c.indexOf(":",C+1);z!==-1&&(b===-1||z<b)?(f.hostname=c.substring(0,h)||null,f.port=null):(g=c.substring(0,h).split(":"),f.hostname=g[0]||null,f.port=g[1]||null)}return f.hostname&&c.substring(h).charAt(0)!=="/"&&(h++,c="/"+c),f.preventInvalidHostname&&r.ensureValidHostname(f.hostname,f.protocol),f.port&&r.ensureValidPort(f.port),c.substring(h)||"/"},r.parseAuthority=function(c,f){return c=r.parseUserinfo(c,f),r.parseHost(c,f)},r.parseUserinfo=function(c,f){var h=c,y=c.indexOf("\\");y!==-1&&(c=c.replace(/\\/g,"/"));var g=c.indexOf("/"),C=c.lastIndexOf("@",g>-1?g:c.length-1),b;return C>-1&&(g===-1||C<g)?(b=c.substring(0,C).split(":"),f.username=b[0]?r.decode(b[0]):null,b.shift(),f.password=b[0]?r.decode(b.join(":")):null,c=h.substring(C+1)):(f.username=null,f.password=null),c},r.parseQuery=function(c,f){if(!c)return{};if(c=c.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!c)return{};for(var h={},y=c.split("&"),g=y.length,C,b,z,N=0;N<g;N++)C=y[N].split("="),b=r.decodeQuery(C.shift(),f),z=C.length?r.decodeQuery(C.join("="),f):null,b!=="__proto__"&&(d.call(h,b)?((typeof h[b]=="string"||h[b]===null)&&(h[b]=[h[b]]),h[b].push(z)):h[b]=z);return h},r.build=function(c){var f="",h=!1;return c.protocol&&(f+=c.protocol+":"),!c.urn&&(f||c.hostname)&&(f+="//",h=!0),f+=r.buildAuthority(c)||"",typeof c.path=="string"&&(c.path.charAt(0)!=="/"&&h&&(f+="/"),f+=c.path),typeof c.query=="string"&&c.query&&(f+="?"+c.query),typeof c.fragment=="string"&&c.fragment&&(f+="#"+c.fragment),f},r.buildHost=function(c){var f="";if(c.hostname)r.ip6_expression.test(c.hostname)?f+="["+c.hostname+"]":f+=c.hostname;else return"";return c.port&&(f+=":"+c.port),f},r.buildAuthority=function(c){return r.buildUserinfo(c)+r.buildHost(c)},r.buildUserinfo=function(c){var f="";return c.username&&(f+=r.encode(c.username)),c.password&&(f+=":"+r.encode(c.password)),f&&(f+="@"),f},r.buildQuery=function(c,f,h){var y="",g,C,b,z;for(C in c)if(C!=="__proto__"&&d.call(c,C))if(w(c[C]))for(g={},b=0,z=c[C].length;b<z;b++)c[C][b]!==void 0&&g[c[C][b]+""]===void 0&&(y+="&"+r.buildQueryParameter(C,c[C][b],h),f!==!0&&(g[c[C][b]+""]=!0));else c[C]!==void 0&&(y+="&"+r.buildQueryParameter(C,c[C],h));return y.substring(1)},r.buildQueryParameter=function(c,f,h){return r.encodeQuery(c,h)+(f!==null?"="+r.encodeQuery(f,h):"")},r.addQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.addQuery(c,y,f[y]);else if(typeof f=="string"){if(c[f]===void 0){c[f]=h;return}else typeof c[f]=="string"&&(c[f]=[c[f]]);w(h)||(h=[h]),c[f]=(c[f]||[]).concat(h)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},r.setQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.setQuery(c,y,f[y]);else if(typeof f=="string")c[f]=h===void 0?null:h;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},r.removeQuery=function(c,f,h){var y,g,C;if(w(f))for(y=0,g=f.length;y<g;y++)c[f[y]]=void 0;else if(l(f)==="RegExp")for(C in c)f.test(C)&&(c[C]=void 0);else if(typeof f=="object")for(C in f)d.call(f,C)&&r.removeQuery(c,C,f[C]);else if(typeof f=="string")h!==void 0?l(h)==="RegExp"?!w(c[f])&&h.test(c[f])?c[f]=void 0:c[f]=T(c[f],h):c[f]===String(h)&&(!w(h)||h.length===1)?c[f]=void 0:w(c[f])&&(c[f]=T(c[f],h)):c[f]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},r.hasQuery=function(c,f,h,y){switch(l(f)){case"String":break;case"RegExp":for(var g in c)if(d.call(c,g)&&f.test(g)&&(h===void 0||r.hasQuery(c,g,h)))return!0;return!1;case"Object":for(var C in f)if(d.call(f,C)&&!r.hasQuery(c,C,f[C]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(h)){case"Undefined":return f in c;case"Boolean":var b=!!(w(c[f])?c[f].length:c[f]);return h===b;case"Function":return!!h(c[f],f,c);case"Array":if(!w(c[f]))return!1;var z=y?v:P;return z(c[f],h);case"RegExp":return w(c[f])?y?v(c[f],h):!1:!!(c[f]&&c[f].match(h));case"Number":h=String(h);case"String":return w(c[f])?y?v(c[f],h):!1:c[f]===h;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},r.joinPaths=function(){for(var c=[],f=[],h=0,y=0;y<arguments.length;y++){var g=new r(arguments[y]);c.push(g);for(var C=g.segment(),b=0;b<C.length;b++)typeof C[b]=="string"&&f.push(C[b]),C[b]&&h++}if(!f.length||!h)return new r("");var z=new r("").segment(f);return(c[0].path()===""||c[0].path().slice(0,1)==="/")&&z.path("/"+z.path()),z.normalize()},r.commonPath=function(c,f){var h=Math.min(c.length,f.length),y;for(y=0;y<h;y++)if(c.charAt(y)!==f.charAt(y)){y--;break}return y<1?c.charAt(0)===f.charAt(0)&&c.charAt(0)==="/"?"/":"":((c.charAt(y)!=="/"||f.charAt(y)!=="/")&&(y=c.substring(0,y).lastIndexOf("/")),c.substring(0,y+1))},r.withinString=function(c,f,h){h||(h={});var y=h.start||r.findUri.start,g=h.end||r.findUri.end,C=h.trim||r.findUri.trim,b=h.parens||r.findUri.parens,z=/[a-z0-9-]=["']?$/i;for(y.lastIndex=0;;){var N=y.exec(c);if(!N)break;var Y=N.index;if(h.ignoreHtml){var ie=c.slice(Math.max(Y-3,0),Y);if(ie&&z.test(ie))continue}for(var ae=Y+c.slice(Y).search(g),te=c.slice(Y,ae),ue=-1;;){var de=b.exec(te);if(!de)break;var ye=de.index+de[0].length;ue=Math.max(ue,ye)}if(ue>-1?te=te.slice(0,ue)+te.slice(ue).replace(C,""):te=te.replace(C,""),!(te.length<=N[0].length)&&!(h.ignore&&h.ignore.test(te))){ae=Y+te.length;var me=f(te,Y,ae,c);if(me===void 0){y.lastIndex=ae;continue}me=String(me),c=c.slice(0,Y)+me+c.slice(ae),y.lastIndex=Y+me.length}}return y.lastIndex=0,c},r.ensureValidHostname=function(c,f){var h=!!c,y=!!f,g=!1;if(y&&(g=v(r.hostProtocols,f)),g&&!h)throw new TypeError("Hostname cannot be empty, if protocol is "+f);if(c&&c.match(r.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(c).match(r.invalid_hostname_characters))throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_]')}},r.ensureValidPort=function(c){if(c){var f=Number(c);if(!(a(f)&&f>0&&f<65536))throw new TypeError('Port "'+c+'" is not a valid port')}},r.noConflict=function(c){if(c){var f={URI:this.noConflict()};return o.URITemplate&&typeof o.URITemplate.noConflict=="function"&&(f.URITemplate=o.URITemplate.noConflict()),o.IPv6&&typeof o.IPv6.noConflict=="function"&&(f.IPv6=o.IPv6.noConflict()),o.SecondLevelDomains&&typeof o.SecondLevelDomains.noConflict=="function"&&(f.SecondLevelDomains=o.SecondLevelDomains.noConflict()),f}else o.URI===this&&(o.URI=i);return this},u.build=function(c){return c===!0?this._deferred_build=!0:(c===void 0||this._deferred_build)&&(this._string=r.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new r(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function H(c){return function(f,h){return f===void 0?this._parts[c]||"":(this._parts[c]=f||null,this.build(!h),this)}}function ee(c,f){return function(h,y){return h===void 0?this._parts[c]||"":(h!==null&&(h=h+"",h.charAt(0)===f&&(h=h.substring(1))),this._parts[c]=h,this.build(!y),this)}}u.protocol=H("protocol"),u.username=H("username"),u.password=H("password"),u.hostname=H("hostname"),u.port=H("port"),u.query=ee("query","?"),u.fragment=ee("fragment","#"),u.search=function(c,f){var h=this.query(c,f);return typeof h=="string"&&h.length?"?"+h:h},u.hash=function(c,f){var h=this.fragment(c,f);return typeof h=="string"&&h.length?"#"+h:h},u.pathname=function(c,f){if(c===void 0||c===!0){var h=this._parts.path||(this._parts.hostname?"/":"");return c?(this._parts.urn?r.decodeUrnPath:r.decodePath)(h):h}else return this._parts.urn?this._parts.path=c?r.recodeUrnPath(c):"":this._parts.path=c?r.recodePath(c):"/",this.build(!f),this},u.path=u.pathname,u.href=function(c,f){var h;if(c===void 0)return this.toString();this._string="",this._parts=r._parts();var y=c instanceof r,g=typeof c=="object"&&(c.hostname||c.path||c.pathname);if(c.nodeName){var C=r.getDomAttribute(c);c=c[C]||"",g=!1}if(!y&&g&&c.pathname!==void 0&&(c=c.toString()),typeof c=="string"||c instanceof String)this._parts=r.parse(String(c),this._parts);else if(y||g){var b=y?c._parts:c;for(h in b)h!=="query"&&d.call(this._parts,h)&&(this._parts[h]=b[h]);b.query&&this.query(b.query,!1)}else throw new TypeError("invalid input");return this.build(!f),this},u.is=function(c){var f=!1,h=!1,y=!1,g=!1,C=!1,b=!1,z=!1,N=!this._parts.urn;switch(this._parts.hostname&&(N=!1,h=r.ip4_expression.test(this._parts.hostname),y=r.ip6_expression.test(this._parts.hostname),f=h||y,g=!f,C=g&&n&&n.has(this._parts.hostname),b=g&&r.idn_expression.test(this._parts.hostname),z=g&&r.punycode_expression.test(this._parts.hostname)),c.toLowerCase()){case"relative":return N;case"absolute":return!N;case"domain":case"name":return g;case"sld":return C;case"ip":return f;case"ip4":case"ipv4":case"inet4":return h;case"ip6":case"ipv6":case"inet6":return y;case"idn":return b;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return z}return null};var re=u.protocol,Z=u.port,oe=u.hostname;u.protocol=function(c,f){if(c&&(c=c.replace(/:(\/\/)?$/,""),!c.match(r.protocol_expression)))throw new TypeError('Protocol "'+c+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return re.call(this,c,f)},u.scheme=u.protocol,u.port=function(c,f){return this._parts.urn?c===void 0?"":this:(c!==void 0&&(c===0&&(c=null),c&&(c+="",c.charAt(0)===":"&&(c=c.substring(1)),r.ensureValidPort(c))),Z.call(this,c,f))},u.hostname=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c!==void 0){var h={preventInvalidHostname:this._parts.preventInvalidHostname},y=r.parseHost(c,h);if(y!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');c=h.hostname,this._parts.preventInvalidHostname&&r.ensureValidHostname(c,this._parts.protocol)}return oe.call(this,c,f)},u.origin=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=this.protocol(),y=this.authority();return y?(h?h+"://":"")+this.authority():""}else{var g=r(c);return this.protocol(g.protocol()).authority(g.authority()).build(!f),this}},u.host=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildHost(this._parts):"";var h=r.parseHost(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.authority=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildAuthority(this._parts):"";var h=r.parseAuthority(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.userinfo=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=r.buildUserinfo(this._parts);return h&&h.substring(0,h.length-1)}else return c[c.length-1]!=="@"&&(c+="@"),r.parseUserinfo(c,this._parts),this.build(!f),this},u.resource=function(c,f){var h;return c===void 0?this.path()+this.search()+this.hash():(h=r.parse(c),this._parts.path=h.path,this._parts.query=h.query,this._parts.fragment=h.fragment,this.build(!f),this)},u.subdomain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,h)||""}else{var y=this._parts.hostname.length-this.domain().length,g=this._parts.hostname.substring(0,y),C=new RegExp("^"+m(g));if(c&&c.charAt(c.length-1)!=="."&&(c+="."),c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return c&&r.ensureValidHostname(c,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(C,c),this.build(!f),this}},u.domain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.match(/\./g);if(h&&h.length<2)return this._parts.hostname;var y=this._parts.hostname.length-this.tld(f).length-1;return y=this._parts.hostname.lastIndexOf(".",y-1)+1,this._parts.hostname.substring(y)||""}else{if(!c)throw new TypeError("cannot set domain empty");if(c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(r.ensureValidHostname(c,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=c;else{var g=new RegExp(m(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(g,c)}return this.build(!f),this}},u.tld=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.lastIndexOf("."),y=this._parts.hostname.substring(h+1);return f!==!0&&n&&n.list[y.toLowerCase()]&&n.get(this._parts.hostname)||y}else{var g;if(c)if(c.match(/[^a-zA-Z0-9-]/))if(n&&n.is(c))g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c);else throw new TypeError('TLD "'+c+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c)}else throw new TypeError("cannot set TLD empty");return this.build(!f),this}},u.directory=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var h=this._parts.path.length-this.filename().length-1,y=this._parts.path.substring(0,h)||(this._parts.hostname?"/":"");return c?r.decodePath(y):y}else{var g=this._parts.path.length-this.filename().length,C=this._parts.path.substring(0,g),b=new RegExp("^"+m(C));return this.is("relative")||(c||(c="/"),c.charAt(0)!=="/"&&(c="/"+c)),c&&c.charAt(c.length-1)!=="/"&&(c+="/"),c=r.recodePath(c),this._parts.path=this._parts.path.replace(b,c),this.build(!f),this}},u.filename=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c!="string"){if(!this._parts.path||this._parts.path==="/")return"";var h=this._parts.path.lastIndexOf("/"),y=this._parts.path.substring(h+1);return c?r.decodePathSegment(y):y}else{var g=!1;c.charAt(0)==="/"&&(c=c.substring(1)),c.match(/\.?\//)&&(g=!0);var C=new RegExp(m(this.filename())+"$");return c=r.recodePath(c),this._parts.path=this._parts.path.replace(C,c),g?this.normalizePath(f):this.build(!f),this}},u.suffix=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path||this._parts.path==="/")return"";var h=this.filename(),y=h.lastIndexOf("."),g,C;return y===-1?"":(g=h.substring(y+1),C=/^[a-z0-9%]+$/i.test(g)?g:"",c?r.decodePathSegment(C):C)}else{c.charAt(0)==="."&&(c=c.substring(1));var b=this.suffix(),z;if(b)c?z=new RegExp(m(b)+"$"):z=new RegExp(m("."+b)+"$");else{if(!c)return this;this._parts.path+="."+r.recodePath(c)}return z&&(c=r.recodePath(c),this._parts.path=this._parts.path.replace(z,c)),this.build(!f),this}},u.segment=function(c,f,h){var y=this._parts.urn?":":"/",g=this.path(),C=g.substring(0,1)==="/",b=g.split(y);if(c!==void 0&&typeof c!="number"&&(h=f,f=c,c=void 0),c!==void 0&&typeof c!="number")throw new Error('Bad segment "'+c+'", must be 0-based integer');if(C&&b.shift(),c<0&&(c=Math.max(b.length+c,0)),f===void 0)return c===void 0?b:b[c];if(c===null||b[c]===void 0)if(w(f)){b=[];for(var z=0,N=f.length;z<N;z++)!f[z].length&&(!b.length||!b[b.length-1].length)||(b.length&&!b[b.length-1].length&&b.pop(),b.push(j(f[z])))}else(f||typeof f=="string")&&(f=j(f),b[b.length-1]===""?b[b.length-1]=f:b.push(f));else f?b[c]=j(f):b.splice(c,1);return C&&b.unshift(""),this.path(b.join(y),h)},u.segmentCoded=function(c,f,h){var y,g,C;if(typeof c!="number"&&(h=f,f=c,c=void 0),f===void 0){if(y=this.segment(c,f,h),!w(y))y=y!==void 0?r.decode(y):void 0;else for(g=0,C=y.length;g<C;g++)y[g]=r.decode(y[g]);return y}if(!w(f))f=typeof f=="string"||f instanceof String?r.encode(f):f;else for(g=0,C=f.length;g<C;g++)f[g]=r.encode(f[g]);return this.segment(c,f,h)};var J=u.query;return u.query=function(c,f){if(c===!0)return r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="function"){var h=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace),y=c.call(this,h);return this._parts.query=r.buildQuery(y||h,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this}else return c!==void 0&&typeof c!="string"?(this._parts.query=r.buildQuery(c,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this):J.call(this,c,f)},u.setQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="string"||c instanceof String)y[c]=f!==void 0?f:null;else if(typeof c=="object")for(var g in c)d.call(c,g)&&(y[g]=c[g]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.addQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.addQuery(y,c,f===void 0?null:f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.removeQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.removeQuery(y,c,f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.hasQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.hasQuery(y,c,f,h)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(c){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!c)),this},u.normalizeHostname=function(c){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!c)),this},u.normalizePort=function(c){return typeof this._parts.protocol=="string"&&this._parts.port===r.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!c)),this},u.normalizePath=function(c){var f=this._parts.path;if(!f)return this;if(this._parts.urn)return this._parts.path=r.recodeUrnPath(this._parts.path),this.build(!c),this;if(this._parts.path==="/")return this;f=r.recodePath(f);var h,y="",g,C;for(f.charAt(0)!=="/"&&(h=!0,f="/"+f),(f.slice(-3)==="/.."||f.slice(-2)==="/.")&&(f+="/"),f=f.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),h&&(y=f.substring(1).match(/^(\.\.\/)+/)||"",y&&(y=y[0]));g=f.search(/\/\.\.(\/|$)/),g!==-1;){if(g===0){f=f.substring(3);continue}C=f.substring(0,g).lastIndexOf("/"),C===-1&&(C=g),f=f.substring(0,C)+f.substring(g+3)}return h&&this.is("relative")&&(f=y+f.substring(1)),this._parts.path=f,this.build(!c),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(c){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(r.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!c)),this},u.normalizeFragment=function(c){return this._parts.fragment||(this._parts.fragment=null,this.build(!c)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var c=r.encode,f=r.decode;r.encode=escape,r.decode=decodeURIComponent;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.unicode=function(){var c=r.encode,f=r.decode;r.encode=k,r.decode=unescape;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.readable=function(){var c=this.clone();c.username("").password("").normalize();var f="";if(c._parts.protocol&&(f+=c._parts.protocol+"://"),c._parts.hostname&&(c.is("punycode")&&e?(f+=e.toUnicode(c._parts.hostname),c._parts.port&&(f+=":"+c._parts.port)):f+=c.host()),c._parts.hostname&&c._parts.path&&c._parts.path.charAt(0)!=="/"&&(f+="/"),f+=c.path(!0),c._parts.query){for(var h="",y=0,g=c._parts.query.split("&"),C=g.length;y<C;y++){var b=(g[y]||"").split("=");h+="&"+r.decodeQuery(b[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),b[1]!==void 0&&(h+="="+r.decodeQuery(b[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}f+="?"+h.substring(1)}return f+=r.decodeQuery(c.hash(),!0),f},u.absoluteTo=function(c){var f=this.clone(),h=["protocol","username","password","hostname","port"],y,g,C;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c instanceof r||(c=new r(c)),f._parts.protocol||(f._parts.protocol=c._parts.protocol,this._parts.hostname))return f;for(g=0;C=h[g];g++)f._parts[C]=c._parts[C];return f._parts.path?(f._parts.path.substring(-2)===".."&&(f._parts.path+="/"),f.path().charAt(0)!=="/"&&(y=c.directory(),y=y||(c.path().indexOf("/")===0?"/":""),f._parts.path=(y?y+"/":"")+f._parts.path,f.normalizePath())):(f._parts.path=c._parts.path,f._parts.query||(f._parts.query=c._parts.query)),f.build(),f},u.relativeTo=function(c){var f=this.clone().normalize(),h,y,g,C,b;if(f._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c=new r(c).normalize(),h=f._parts,y=c._parts,C=f.path(),b=c.path(),C.charAt(0)!=="/")throw new Error("URI is already relative");if(b.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(h.protocol===y.protocol&&(h.protocol=null),h.username!==y.username||h.password!==y.password||h.protocol!==null||h.username!==null||h.password!==null)return f.build();if(h.hostname===y.hostname&&h.port===y.port)h.hostname=null,h.port=null;else return f.build();if(C===b)return h.path="",f.build();if(g=r.commonPath(C,b),!g)return f.build();var z=y.path.substring(g.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return h.path=z+h.path.substring(g.length)||"./",f.build()},u.equals=function(c){var f=this.clone(),h=new r(c),y={},g={},C={},b,z,N;if(f.normalize(),h.normalize(),f.toString()===h.toString())return!0;if(b=f.query(),z=h.query(),f.query(""),h.query(""),f.toString()!==h.toString()||b.length!==z.length)return!1;y=r.parseQuery(b,this._parts.escapeQuerySpace),g=r.parseQuery(z,this._parts.escapeQuerySpace);for(N in y)if(d.call(y,N)){if(w(y[N])){if(!P(y[N],g[N]))return!1}else if(y[N]!==g[N])return!1;C[N]=!0}for(N in g)if(d.call(g,N)&&!C[N])return!1;return!0},u.preventInvalidHostname=function(c){return this._parts.preventInvalidHostname=!!c,this},u.duplicateQueryParameters=function(c){return this._parts.duplicateQueryParameters=!!c,this},u.escapeQuerySpace=function(c){return this._parts.escapeQuerySpace=!!c,this},r})});function M(e,t,n,o){this.x=O(e,0),this.y=O(t,0),this.z=O(n,0),this.w=O(o,0)}M.fromElements=function(e,t,n,o,i){return p(i)?(i.x=e,i.y=t,i.z=n,i.w=o,i):new M(e,t,n,o)};M.fromColor=function(e,t){return s.typeOf.object("color",e),p(t)?(t.x=e.red,t.y=e.green,t.z=e.blue,t.w=e.alpha,t):new M(e.red,e.green,e.blue,e.alpha)};M.clone=function(e,t){if(p(e))return p(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new M(e.x,e.y,e.z,e.w)};M.packedLength=4;M.pack=function(e,t,n){return s.typeOf.object("value",e),s.defined("array",t),n=O(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t};M.unpack=function(e,t,n){return s.defined("array",e),t=O(t,0),p(n)||(n=new M),n.x=e[t++],n.y=e[t++],n.z=e[t++],n.w=e[t],n};M.packArray=function(e,t){s.defined("array",e);let n=e.length,o=n*4;if(!p(t))t=new Array(o);else{if(!Array.isArray(t)&&t.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");t.length!==o&&(t.length=o)}for(let i=0;i<n;++i)M.pack(e[i],t,i*4);return t};M.unpackArray=function(e,t){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let n=e.length;p(t)?t.length=n/4:t=new Array(n/4);for(let o=0;o<n;o+=4){let i=o/4;t[i]=M.unpack(e,o,t[i])}return t};M.fromArray=M.unpack;M.maximumComponent=function(e){return s.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)};M.minimumComponent=function(e){return s.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)};M.minimumByComponent=function(e,t,n){return s.typeOf.object("first",e),s.typeOf.object("second",t),s.typeOf.object("result",n),n.x=Math.min(e.x,t.x),n.y=Math.min(e.y,t.y),n.z=Math.min(e.z,t.z),n.w=Math.min(e.w,t.w),n};M.maximumByComponent=function(e,t,n){return s.typeOf.object("first",e),s.typeOf.object("second",t),s.typeOf.object("result",n),n.x=Math.max(e.x,t.x),n.y=Math.max(e.y,t.y),n.z=Math.max(e.z,t.z),n.w=Math.max(e.w,t.w),n};M.clamp=function(e,t,n,o){s.typeOf.object("value",e),s.typeOf.object("min",t),s.typeOf.object("max",n),s.typeOf.object("result",o);let i=E.clamp(e.x,t.x,n.x),r=E.clamp(e.y,t.y,n.y),a=E.clamp(e.z,t.z,n.z),u=E.clamp(e.w,t.w,n.w);return o.x=i,o.y=r,o.z=a,o.w=u,o};M.magnitudeSquared=function(e){return s.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};M.magnitude=function(e){return Math.sqrt(M.magnitudeSquared(e))};var Et=new M;M.distance=function(e,t){return s.typeOf.object("left",e),s.typeOf.object("right",t),M.subtract(e,t,Et),M.magnitude(Et)};M.distanceSquared=function(e,t){return s.typeOf.object("left",e),s.typeOf.object("right",t),M.subtract(e,t,Et),M.magnitudeSquared(Et)};M.normalize=function(e,t){s.typeOf.object("cartesian",e),s.typeOf.object("result",t);let n=M.magnitude(e);if(t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,isNaN(t.x)||isNaN(t.y)||isNaN(t.z)||isNaN(t.w))throw new A("normalized result is not a number");return t};M.dot=function(e,t){return s.typeOf.object("left",e),s.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w};M.multiplyComponents=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x*t.x,n.y=e.y*t.y,n.z=e.z*t.z,n.w=e.w*t.w,n};M.divideComponents=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x/t.x,n.y=e.y/t.y,n.z=e.z/t.z,n.w=e.w/t.w,n};M.add=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n};M.subtract=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n};M.multiplyByScalar=function(e,t,n){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n};M.divideByScalar=function(e,t,n){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n};M.negate=function(e,t){return s.typeOf.object("cartesian",e),s.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t};M.abs=function(e,t){return s.typeOf.object("cartesian",e),s.typeOf.object("result",t),t.x=Math.abs(e.x),t.y=Math.abs(e.y),t.z=Math.abs(e.z),t.w=Math.abs(e.w),t};var Tn=new M;M.lerp=function(e,t,n,o){return s.typeOf.object("start",e),s.typeOf.object("end",t),s.typeOf.number("t",n),s.typeOf.object("result",o),M.multiplyByScalar(t,n,Tn),o=M.multiplyByScalar(e,1-n,o),M.add(Tn,o,o)};var No=new M;M.mostOrthogonalAxis=function(e,t){s.typeOf.object("cartesian",e),s.typeOf.object("result",t);let n=M.normalize(e,No);return M.abs(n,n),n.x<=n.y?n.x<=n.z?n.x<=n.w?t=M.clone(M.UNIT_X,t):t=M.clone(M.UNIT_W,t):n.z<=n.w?t=M.clone(M.UNIT_Z,t):t=M.clone(M.UNIT_W,t):n.y<=n.z?n.y<=n.w?t=M.clone(M.UNIT_Y,t):t=M.clone(M.UNIT_W,t):n.z<=n.w?t=M.clone(M.UNIT_Z,t):t=M.clone(M.UNIT_W,t),t};M.equals=function(e,t){return e===t||p(e)&&p(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w};M.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]&&e.w===t[n+3]};M.equalsEpsilon=function(e,t,n,o){return e===t||p(e)&&p(t)&&E.equalsEpsilon(e.x,t.x,n,o)&&E.equalsEpsilon(e.y,t.y,n,o)&&E.equalsEpsilon(e.z,t.z,n,o)&&E.equalsEpsilon(e.w,t.w,n,o)};M.ZERO=Object.freeze(new M(0,0,0,0));M.ONE=Object.freeze(new M(1,1,1,1));M.UNIT_X=Object.freeze(new M(1,0,0,0));M.UNIT_Y=Object.freeze(new M(0,1,0,0));M.UNIT_Z=Object.freeze(new M(0,0,1,0));M.UNIT_W=Object.freeze(new M(0,0,0,1));M.prototype.clone=function(e){return M.clone(this,e)};M.prototype.equals=function(e){return M.equals(this,e)};M.prototype.equalsEpsilon=function(e,t,n){return M.equalsEpsilon(this,e,t,n)};M.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Jt=new Float32Array(1),le=new Uint8Array(Jt.buffer),ko=new Uint32Array([287454020]),Fo=new Uint8Array(ko.buffer),vn=Fo[0]===68;M.packFloat=function(e,t){return s.typeOf.number("value",e),p(t)||(t=new M),Jt[0]=e,vn?(t.x=le[0],t.y=le[1],t.z=le[2],t.w=le[3]):(t.x=le[3],t.y=le[2],t.z=le[1],t.w=le[0]),t};M.unpackFloat=function(e){return s.typeOf.object("packedFloat",e),vn?(le[0]=e.x,le[1]=e.y,le[2]=e.z,le[3]=e.w):(le[0]=e.w,le[1]=e.z,le[2]=e.y,le[3]=e.x),Jt[0]};var qe=M;function S(e,t,n,o,i,r,a,u,d,m,l,w,T,v,P,j){this[0]=O(e,0),this[1]=O(i,0),this[2]=O(d,0),this[3]=O(T,0),this[4]=O(t,0),this[5]=O(r,0),this[6]=O(m,0),this[7]=O(v,0),this[8]=O(n,0),this[9]=O(a,0),this[10]=O(l,0),this[11]=O(P,0),this[12]=O(o,0),this[13]=O(u,0),this[14]=O(w,0),this[15]=O(j,0)}S.packedLength=16;S.pack=function(e,t,n){return s.typeOf.object("value",e),s.defined("array",t),n=O(n,0),t[n++]=e[0],t[n++]=e[1],t[n++]=e[2],t[n++]=e[3],t[n++]=e[4],t[n++]=e[5],t[n++]=e[6],t[n++]=e[7],t[n++]=e[8],t[n++]=e[9],t[n++]=e[10],t[n++]=e[11],t[n++]=e[12],t[n++]=e[13],t[n++]=e[14],t[n]=e[15],t};S.unpack=function(e,t,n){return s.defined("array",e),t=O(t,0),p(n)||(n=new S),n[0]=e[t++],n[1]=e[t++],n[2]=e[t++],n[3]=e[t++],n[4]=e[t++],n[5]=e[t++],n[6]=e[t++],n[7]=e[t++],n[8]=e[t++],n[9]=e[t++],n[10]=e[t++],n[11]=e[t++],n[12]=e[t++],n[13]=e[t++],n[14]=e[t++],n[15]=e[t],n};S.packArray=function(e,t){s.defined("array",e);let n=e.length,o=n*16;if(!p(t))t=new Array(o);else{if(!Array.isArray(t)&&t.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 16 elements");t.length!==o&&(t.length=o)}for(let i=0;i<n;++i)S.pack(e[i],t,i*16);return t};S.unpackArray=function(e,t){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,16),e.length%16!==0)throw new A("array length must be a multiple of 16.");let n=e.length;p(t)?t.length=n/16:t=new Array(n/16);for(let o=0;o<n;o+=16){let i=o/16;t[i]=S.unpack(e,o,t[i])}return t};S.clone=function(e,t){if(p(e))return p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t):new S(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])};S.fromArray=S.unpack;S.fromColumnMajorArray=function(e,t){return s.defined("values",e),S.clone(e,t)};S.fromRowMajorArray=function(e,t){return s.defined("values",e),p(t)?(t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15],t):new S(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])};S.fromRotationTranslation=function(e,t,n){return s.typeOf.object("rotation",e),t=O(t,_.ZERO),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=0,n[4]=e[3],n[5]=e[4],n[6]=e[5],n[7]=0,n[8]=e[6],n[9]=e[7],n[10]=e[8],n[11]=0,n[12]=t.x,n[13]=t.y,n[14]=t.z,n[15]=1,n):new S(e[0],e[3],e[6],t.x,e[1],e[4],e[7],t.y,e[2],e[5],e[8],t.z,0,0,0,1)};S.fromTranslationQuaternionRotationScale=function(e,t,n,o){s.typeOf.object("translation",e),s.typeOf.object("rotation",t),s.typeOf.object("scale",n),p(o)||(o=new S);let i=n.x,r=n.y,a=n.z,u=t.x*t.x,d=t.x*t.y,m=t.x*t.z,l=t.x*t.w,w=t.y*t.y,T=t.y*t.z,v=t.y*t.w,P=t.z*t.z,j=t.z*t.w,q=t.w*t.w,k=u-w-P+q,x=2*(d-j),F=2*(m+v),B=2*(d+j),Q=-u+w-P+q,H=2*(T-l),ee=2*(m-v),re=2*(T+l),Z=-u-w+P+q;return o[0]=k*i,o[1]=B*i,o[2]=ee*i,o[3]=0,o[4]=x*r,o[5]=Q*r,o[6]=re*r,o[7]=0,o[8]=F*a,o[9]=H*a,o[10]=Z*a,o[11]=0,o[12]=e.x,o[13]=e.y,o[14]=e.z,o[15]=1,o};S.fromTranslationRotationScale=function(e,t){return s.typeOf.object("translationRotationScale",e),S.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,t)};S.fromTranslation=function(e,t){return s.typeOf.object("translation",e),S.fromRotationTranslation(W.IDENTITY,e,t)};S.fromScale=function(e,t){return s.typeOf.object("scale",e),p(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e.y,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e.z,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t):new S(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)};S.fromUniformScale=function(e,t){return s.typeOf.number("scale",e),p(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t):new S(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};S.fromRotation=function(e,t){return s.typeOf.object("rotation",e),p(t)||(t=new S),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=0,t[4]=e[3],t[5]=e[4],t[6]=e[5],t[7]=0,t[8]=e[6],t[9]=e[7],t[10]=e[8],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t};var Je=new _,Ge=new _,ut=new _;S.fromCamera=function(e,t){s.typeOf.object("camera",e);let n=e.position,o=e.direction,i=e.up;s.typeOf.object("camera.position",n),s.typeOf.object("camera.direction",o),s.typeOf.object("camera.up",i),_.normalize(o,Je),_.normalize(_.cross(Je,i,Ge),Ge),_.normalize(_.cross(Ge,Je,ut),ut);let r=Ge.x,a=Ge.y,u=Ge.z,d=Je.x,m=Je.y,l=Je.z,w=ut.x,T=ut.y,v=ut.z,P=n.x,j=n.y,q=n.z,k=r*-P+a*-j+u*-q,x=w*-P+T*-j+v*-q,F=d*P+m*j+l*q;return p(t)?(t[0]=r,t[1]=w,t[2]=-d,t[3]=0,t[4]=a,t[5]=T,t[6]=-m,t[7]=0,t[8]=u,t[9]=v,t[10]=-l,t[11]=0,t[12]=k,t[13]=x,t[14]=F,t[15]=1,t):new S(r,a,u,k,w,T,v,x,-d,-m,-l,F,0,0,0,1)};S.computePerspectiveFieldOfView=function(e,t,n,o,i){s.typeOf.number.greaterThan("fovY",e,0),s.typeOf.number.lessThan("fovY",e,Math.PI),s.typeOf.number.greaterThan("near",n,0),s.typeOf.number.greaterThan("far",o,0),s.typeOf.object("result",i);let a=1/Math.tan(e*.5),u=a/t,d=(o+n)/(n-o),m=2*o*n/(n-o);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=a,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=d,i[11]=-1,i[12]=0,i[13]=0,i[14]=m,i[15]=0,i};S.computeOrthographicOffCenter=function(e,t,n,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",t),s.typeOf.number("bottom",n),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=1/(t-e),d=1/(o-n),m=1/(r-i),l=-(t+e)*u,w=-(o+n)*d,T=-(r+i)*m;return u*=2,d*=2,m*=-2,a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=m,a[11]=0,a[12]=l,a[13]=w,a[14]=T,a[15]=1,a};S.computePerspectiveOffCenter=function(e,t,n,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",t),s.typeOf.number("bottom",n),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=2*i/(t-e),d=2*i/(o-n),m=(t+e)/(t-e),l=(o+n)/(o-n),w=-(r+i)/(r-i),T=-1,v=-2*r*i/(r-i);return a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=m,a[9]=l,a[10]=w,a[11]=T,a[12]=0,a[13]=0,a[14]=v,a[15]=0,a};S.computeInfinitePerspectiveOffCenter=function(e,t,n,o,i,r){s.typeOf.number("left",e),s.typeOf.number("right",t),s.typeOf.number("bottom",n),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.object("result",r);let a=2*i/(t-e),u=2*i/(o-n),d=(t+e)/(t-e),m=(o+n)/(o-n),l=-1,w=-1,T=-2*i;return r[0]=a,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=d,r[9]=m,r[10]=l,r[11]=w,r[12]=0,r[13]=0,r[14]=T,r[15]=0,r};S.computeViewportTransformation=function(e,t,n,o){p(o)||(o=new S),e=O(e,O.EMPTY_OBJECT);let i=O(e.x,0),r=O(e.y,0),a=O(e.width,0),u=O(e.height,0);t=O(t,0),n=O(n,1);let d=a*.5,m=u*.5,l=(n-t)*.5,w=d,T=m,v=l,P=i+d,j=r+m,q=t+l,k=1;return o[0]=w,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=T,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=v,o[11]=0,o[12]=P,o[13]=j,o[14]=q,o[15]=k,o};S.computeView=function(e,t,n,o,i){return s.typeOf.object("position",e),s.typeOf.object("direction",t),s.typeOf.object("up",n),s.typeOf.object("right",o),s.typeOf.object("result",i),i[0]=o.x,i[1]=n.x,i[2]=-t.x,i[3]=0,i[4]=o.y,i[5]=n.y,i[6]=-t.y,i[7]=0,i[8]=o.z,i[9]=n.z,i[10]=-t.z,i[11]=0,i[12]=-_.dot(o,e),i[13]=-_.dot(n,e),i[14]=_.dot(t,e),i[15]=1,i};S.toArray=function(e,t){return s.typeOf.object("matrix",e),p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]};S.getElementIndex=function(e,t){return s.typeOf.number.greaterThanOrEquals("row",t,0),s.typeOf.number.lessThanOrEquals("row",t,3),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,3),e*4+t};S.getColumn=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,3),s.typeOf.object("result",n);let o=t*4,i=e[o],r=e[o+1],a=e[o+2],u=e[o+3];return n.x=i,n.y=r,n.z=a,n.w=u,n};S.setColumn=function(e,t,n,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,3),s.typeOf.object("cartesian",n),s.typeOf.object("result",o),o=S.clone(e,o);let i=t*4;return o[i]=n.x,o[i+1]=n.y,o[i+2]=n.z,o[i+3]=n.w,o};S.getRow=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,3),s.typeOf.object("result",n);let o=e[t],i=e[t+4],r=e[t+8],a=e[t+12];return n.x=o,n.y=i,n.z=r,n.w=a,n};S.setRow=function(e,t,n,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,3),s.typeOf.object("cartesian",n),s.typeOf.object("result",o),o=S.clone(e,o),o[t]=n.x,o[t+4]=n.y,o[t+8]=n.z,o[t+12]=n.w,o};S.setTranslation=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.object("translation",t),s.typeOf.object("result",n),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=t.x,n[13]=t.y,n[14]=t.z,n[15]=e[15],n};var xo=new _;S.setScale=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("scale",t),s.typeOf.object("result",n);let o=S.getScale(e,xo),i=t.x/o.x,r=t.y/o.y,a=t.z/o.z;return n[0]=e[0]*i,n[1]=e[1]*i,n[2]=e[2]*i,n[3]=e[3],n[4]=e[4]*r,n[5]=e[5]*r,n[6]=e[6]*r,n[7]=e[7],n[8]=e[8]*a,n[9]=e[9]*a,n[10]=e[10]*a,n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n};var Lo=new _;S.setUniformScale=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number("scale",t),s.typeOf.object("result",n);let o=S.getScale(e,Lo),i=t/o.x,r=t/o.y,a=t/o.z;return n[0]=e[0]*i,n[1]=e[1]*i,n[2]=e[2]*i,n[3]=e[3],n[4]=e[4]*r,n[5]=e[5]*r,n[6]=e[6]*r,n[7]=e[7],n[8]=e[8]*a,n[9]=e[9]*a,n[10]=e[10]*a,n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n};var Gt=new _;S.getScale=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t.x=_.magnitude(_.fromElements(e[0],e[1],e[2],Gt)),t.y=_.magnitude(_.fromElements(e[4],e[5],e[6],Gt)),t.z=_.magnitude(_.fromElements(e[8],e[9],e[10],Gt)),t};var Cn=new _;S.getMaximumScale=function(e){return S.getScale(e,Cn),_.maximumComponent(Cn)};var Bo=new _;S.setRotation=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let o=S.getScale(e,Bo);return n[0]=t[0]*o.x,n[1]=t[1]*o.x,n[2]=t[2]*o.x,n[3]=e[3],n[4]=t[3]*o.y,n[5]=t[4]*o.y,n[6]=t[5]*o.y,n[7]=e[7],n[8]=t[6]*o.z,n[9]=t[7]*o.z,n[10]=t[8]*o.z,n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n};var Wo=new _;S.getRotation=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=S.getScale(e,Wo);return t[0]=e[0]/n.x,t[1]=e[1]/n.x,t[2]=e[2]/n.x,t[3]=e[4]/n.y,t[4]=e[5]/n.y,t[5]=e[6]/n.y,t[6]=e[8]/n.z,t[7]=e[9]/n.z,t[8]=e[10]/n.z,t};S.multiply=function(e,t,n){s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n);let o=e[0],i=e[1],r=e[2],a=e[3],u=e[4],d=e[5],m=e[6],l=e[7],w=e[8],T=e[9],v=e[10],P=e[11],j=e[12],q=e[13],k=e[14],x=e[15],F=t[0],B=t[1],Q=t[2],H=t[3],ee=t[4],re=t[5],Z=t[6],oe=t[7],J=t[8],c=t[9],f=t[10],h=t[11],y=t[12],g=t[13],C=t[14],b=t[15],z=o*F+u*B+w*Q+j*H,N=i*F+d*B+T*Q+q*H,Y=r*F+m*B+v*Q+k*H,ie=a*F+l*B+P*Q+x*H,ae=o*ee+u*re+w*Z+j*oe,te=i*ee+d*re+T*Z+q*oe,ue=r*ee+m*re+v*Z+k*oe,de=a*ee+l*re+P*Z+x*oe,ye=o*J+u*c+w*f+j*h,me=i*J+d*c+T*f+q*h,he=r*J+m*c+v*f+k*h,X=a*J+l*c+P*f+x*h,_e=o*y+u*g+w*C+j*b,Te=i*y+d*g+T*C+q*b,je=r*y+m*g+v*C+k*b,at=a*y+l*g+P*C+x*b;return n[0]=z,n[1]=N,n[2]=Y,n[3]=ie,n[4]=ae,n[5]=te,n[6]=ue,n[7]=de,n[8]=ye,n[9]=me,n[10]=he,n[11]=X,n[12]=_e,n[13]=Te,n[14]=je,n[15]=at,n};S.add=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n[4]=e[4]+t[4],n[5]=e[5]+t[5],n[6]=e[6]+t[6],n[7]=e[7]+t[7],n[8]=e[8]+t[8],n[9]=e[9]+t[9],n[10]=e[10]+t[10],n[11]=e[11]+t[11],n[12]=e[12]+t[12],n[13]=e[13]+t[13],n[14]=e[14]+t[14],n[15]=e[15]+t[15],n};S.subtract=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n[4]=e[4]-t[4],n[5]=e[5]-t[5],n[6]=e[6]-t[6],n[7]=e[7]-t[7],n[8]=e[8]-t[8],n[9]=e[9]-t[9],n[10]=e[10]-t[10],n[11]=e[11]-t[11],n[12]=e[12]-t[12],n[13]=e[13]-t[13],n[14]=e[14]-t[14],n[15]=e[15]-t[15],n};S.multiplyTransformation=function(e,t,n){s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=e[12],v=e[13],P=e[14],j=t[0],q=t[1],k=t[2],x=t[4],F=t[5],B=t[6],Q=t[8],H=t[9],ee=t[10],re=t[12],Z=t[13],oe=t[14],J=o*j+a*q+m*k,c=i*j+u*q+l*k,f=r*j+d*q+w*k,h=o*x+a*F+m*B,y=i*x+u*F+l*B,g=r*x+d*F+w*B,C=o*Q+a*H+m*ee,b=i*Q+u*H+l*ee,z=r*Q+d*H+w*ee,N=o*re+a*Z+m*oe+T,Y=i*re+u*Z+l*oe+v,ie=r*re+d*Z+w*oe+P;return n[0]=J,n[1]=c,n[2]=f,n[3]=0,n[4]=h,n[5]=y,n[6]=g,n[7]=0,n[8]=C,n[9]=b,n[10]=z,n[11]=0,n[12]=N,n[13]=Y,n[14]=ie,n[15]=1,n};S.multiplyByMatrix3=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("rotation",t),s.typeOf.object("result",n);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=t[0],v=t[1],P=t[2],j=t[3],q=t[4],k=t[5],x=t[6],F=t[7],B=t[8],Q=o*T+a*v+m*P,H=i*T+u*v+l*P,ee=r*T+d*v+w*P,re=o*j+a*q+m*k,Z=i*j+u*q+l*k,oe=r*j+d*q+w*k,J=o*x+a*F+m*B,c=i*x+u*F+l*B,f=r*x+d*F+w*B;return n[0]=Q,n[1]=H,n[2]=ee,n[3]=0,n[4]=re,n[5]=Z,n[6]=oe,n[7]=0,n[8]=J,n[9]=c,n[10]=f,n[11]=0,n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n};S.multiplyByTranslation=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("translation",t),s.typeOf.object("result",n);let o=t.x,i=t.y,r=t.z,a=o*e[0]+i*e[4]+r*e[8]+e[12],u=o*e[1]+i*e[5]+r*e[9]+e[13],d=o*e[2]+i*e[6]+r*e[10]+e[14];return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=a,n[13]=u,n[14]=d,n[15]=e[15],n};S.multiplyByScale=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("scale",t),s.typeOf.object("result",n);let o=t.x,i=t.y,r=t.z;return o===1&&i===1&&r===1?S.clone(e,n):(n[0]=o*e[0],n[1]=o*e[1],n[2]=o*e[2],n[3]=e[3],n[4]=i*e[4],n[5]=i*e[5],n[6]=i*e[6],n[7]=e[7],n[8]=r*e[8],n[9]=r*e[9],n[10]=r*e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n)};S.multiplyByUniformScale=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.number("scale",t),s.typeOf.object("result",n),n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3],n[4]=e[4]*t,n[5]=e[5]*t,n[6]=e[6]*t,n[7]=e[7],n[8]=e[8]*t,n[9]=e[9]*t,n[10]=e[10]*t,n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n};S.multiplyByVector=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",t),s.typeOf.object("result",n);let o=t.x,i=t.y,r=t.z,a=t.w,u=e[0]*o+e[4]*i+e[8]*r+e[12]*a,d=e[1]*o+e[5]*i+e[9]*r+e[13]*a,m=e[2]*o+e[6]*i+e[10]*r+e[14]*a,l=e[3]*o+e[7]*i+e[11]*r+e[15]*a;return n.x=u,n.y=d,n.z=m,n.w=l,n};S.multiplyByPointAsVector=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",t),s.typeOf.object("result",n);let o=t.x,i=t.y,r=t.z,a=e[0]*o+e[4]*i+e[8]*r,u=e[1]*o+e[5]*i+e[9]*r,d=e[2]*o+e[6]*i+e[10]*r;return n.x=a,n.y=u,n.z=d,n};S.multiplyByPoint=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",t),s.typeOf.object("result",n);let o=t.x,i=t.y,r=t.z,a=e[0]*o+e[4]*i+e[8]*r+e[12],u=e[1]*o+e[5]*i+e[9]*r+e[13],d=e[2]*o+e[6]*i+e[10]*r+e[14];return n.x=a,n.y=u,n.z=d,n};S.multiplyByScalar=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n[4]=e[4]*t,n[5]=e[5]*t,n[6]=e[6]*t,n[7]=e[7]*t,n[8]=e[8]*t,n[9]=e[9]*t,n[10]=e[10]*t,n[11]=e[11]*t,n[12]=e[12]*t,n[13]=e[13]*t,n[14]=e[14]*t,n[15]=e[15]*t,n};S.negate=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t[4]=-e[4],t[5]=-e[5],t[6]=-e[6],t[7]=-e[7],t[8]=-e[8],t[9]=-e[9],t[10]=-e[10],t[11]=-e[11],t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=-e[15],t};S.transpose=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=e[1],o=e[2],i=e[3],r=e[6],a=e[7],u=e[11];return t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=n,t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=o,t[9]=r,t[10]=e[10],t[11]=e[14],t[12]=i,t[13]=a,t[14]=u,t[15]=e[15],t};S.abs=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t[4]=Math.abs(e[4]),t[5]=Math.abs(e[5]),t[6]=Math.abs(e[6]),t[7]=Math.abs(e[7]),t[8]=Math.abs(e[8]),t[9]=Math.abs(e[9]),t[10]=Math.abs(e[10]),t[11]=Math.abs(e[11]),t[12]=Math.abs(e[12]),t[13]=Math.abs(e[13]),t[14]=Math.abs(e[14]),t[15]=Math.abs(e[15]),t};S.equals=function(e,t){return e===t||p(e)&&p(t)&&e[12]===t[12]&&e[13]===t[13]&&e[14]===t[14]&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[4]===t[4]&&e[5]===t[5]&&e[6]===t[6]&&e[8]===t[8]&&e[9]===t[9]&&e[10]===t[10]&&e[3]===t[3]&&e[7]===t[7]&&e[11]===t[11]&&e[15]===t[15]};S.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||p(e)&&p(t)&&Math.abs(e[0]-t[0])<=n&&Math.abs(e[1]-t[1])<=n&&Math.abs(e[2]-t[2])<=n&&Math.abs(e[3]-t[3])<=n&&Math.abs(e[4]-t[4])<=n&&Math.abs(e[5]-t[5])<=n&&Math.abs(e[6]-t[6])<=n&&Math.abs(e[7]-t[7])<=n&&Math.abs(e[8]-t[8])<=n&&Math.abs(e[9]-t[9])<=n&&Math.abs(e[10]-t[10])<=n&&Math.abs(e[11]-t[11])<=n&&Math.abs(e[12]-t[12])<=n&&Math.abs(e[13]-t[13])<=n&&Math.abs(e[14]-t[14])<=n&&Math.abs(e[15]-t[15])<=n};S.getTranslation=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t.x=e[12],t.y=e[13],t.z=e[14],t};S.getMatrix3=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t};var Qo=new W,Ho=new W,$o=new qe,Vo=new qe(0,0,0,1);S.inverse=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=e[0],o=e[4],i=e[8],r=e[12],a=e[1],u=e[5],d=e[9],m=e[13],l=e[2],w=e[6],T=e[10],v=e[14],P=e[3],j=e[7],q=e[11],k=e[15],x=T*k,F=v*q,B=w*k,Q=v*j,H=w*q,ee=T*j,re=l*k,Z=v*P,oe=l*q,J=T*P,c=l*j,f=w*P,h=x*u+Q*d+H*m-(F*u+B*d+ee*m),y=F*a+re*d+J*m-(x*a+Z*d+oe*m),g=B*a+Z*u+c*m-(Q*a+re*u+f*m),C=ee*a+oe*u+f*d-(H*a+J*u+c*d),b=F*o+B*i+ee*r-(x*o+Q*i+H*r),z=x*n+Z*i+oe*r-(F*n+re*i+J*r),N=Q*n+re*o+f*r-(B*n+Z*o+c*r),Y=H*n+J*o+c*i-(ee*n+oe*o+f*i);x=i*m,F=r*d,B=o*m,Q=r*u,H=o*d,ee=i*u,re=n*m,Z=r*a,oe=n*d,J=i*a,c=n*u,f=o*a;let ie=x*j+Q*q+H*k-(F*j+B*q+ee*k),ae=F*P+re*q+J*k-(x*P+Z*q+oe*k),te=B*P+Z*j+c*k-(Q*P+re*j+f*k),ue=ee*P+oe*j+f*q-(H*P+J*j+c*q),de=B*T+ee*v+F*w-(H*v+x*w+Q*T),ye=oe*v+x*l+Z*T-(re*T+J*v+F*l),me=re*w+f*v+Q*l-(c*v+B*l+Z*w),he=c*T+H*l+J*w-(oe*w+f*T+ee*l),X=n*h+o*y+i*g+r*C;if(Math.abs(X)<E.EPSILON21){if(W.equalsEpsilon(S.getMatrix3(e,Qo),Ho,E.EPSILON7)&&qe.equals(S.getRow(e,3,$o),Vo))return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=0,t[11]=0,t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=1,t;throw new Se("matrix is not invertible because its determinate is zero.")}return X=1/X,t[0]=h*X,t[1]=y*X,t[2]=g*X,t[3]=C*X,t[4]=b*X,t[5]=z*X,t[6]=N*X,t[7]=Y*X,t[8]=ie*X,t[9]=ae*X,t[10]=te*X,t[11]=ue*X,t[12]=de*X,t[13]=ye*X,t[14]=me*X,t[15]=he*X,t};S.inverseTransformation=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=e[0],o=e[1],i=e[2],r=e[4],a=e[5],u=e[6],d=e[8],m=e[9],l=e[10],w=e[12],T=e[13],v=e[14],P=-n*w-o*T-i*v,j=-r*w-a*T-u*v,q=-d*w-m*T-l*v;return t[0]=n,t[1]=r,t[2]=d,t[3]=0,t[4]=o,t[5]=a,t[6]=m,t[7]=0,t[8]=i,t[9]=u,t[10]=l,t[11]=0,t[12]=P,t[13]=j,t[14]=q,t[15]=1,t};var Yo=new S;S.inverseTranspose=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),S.inverse(S.transpose(e,Yo),t)};S.IDENTITY=Object.freeze(new S(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1));S.ZERO=Object.freeze(new S(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0));S.COLUMN0ROW0=0;S.COLUMN0ROW1=1;S.COLUMN0ROW2=2;S.COLUMN0ROW3=3;S.COLUMN1ROW0=4;S.COLUMN1ROW1=5;S.COLUMN1ROW2=6;S.COLUMN1ROW3=7;S.COLUMN2ROW0=8;S.COLUMN2ROW1=9;S.COLUMN2ROW2=10;S.COLUMN2ROW3=11;S.COLUMN3ROW0=12;S.COLUMN3ROW1=13;S.COLUMN3ROW2=14;S.COLUMN3ROW3=15;Object.defineProperties(S.prototype,{length:{get:function(){return S.packedLength}}});S.prototype.clone=function(e){return S.clone(this,e)};S.prototype.equals=function(e){return S.equals(this,e)};S.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]&&e[4]===t[n+4]&&e[5]===t[n+5]&&e[6]===t[n+6]&&e[7]===t[n+7]&&e[8]===t[n+8]&&e[9]===t[n+9]&&e[10]===t[n+10]&&e[11]===t[n+11]&&e[12]===t[n+12]&&e[13]===t[n+13]&&e[14]===t[n+14]&&e[15]===t[n+15]};S.prototype.equalsEpsilon=function(e,t){return S.equalsEpsilon(this,e,t)};S.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})
(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})
(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})
(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};var G=S;function An(e,t,n){n=O(n,!1);let o={},i=p(e),r=p(t),a,u,d;if(i)for(a in e)e.hasOwnProperty(a)&&(u=e[a],r&&n&&typeof u=="object"&&t.hasOwnProperty(a)?(d=t[a],typeof d=="object"?o[a]=An(u,d,n):o[a]=u):o[a]=u);if(r)for(a in t)t.hasOwnProperty(a)&&!o.hasOwnProperty(a)&&(d=t[a],o[a]=d);return o}var De=An;function Xo(e,t,n){s.defined("array",e),s.defined("itemToFind",t),s.defined("comparator",n);let o=0,i=e.length-1,r,a;for(;o<=i;){if(r=~~((o+i)/2),a=n(e[r],t),a<0){o=r+1;continue}if(a>0){i=r-1;continue}return r}return~(i+1)}var Be=Xo;function Zo(e,t,n,o,i){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=o,this.ut1MinusUtc=i}var pt=Zo;function Jo(e){if(e===null||isNaN(e))throw new A("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var ht=Jo;var jn=[31,28,31,30,31,30,31,31,30,31,30,31];function Go(e,t,n,o,i,r,a,u){e=O(e,1),t=O(t,1),n=O(n,1),o=O(o,0),i=O(i,0),r=O(r,0),a=O(a,0),u=O(u,!1),j(),q(),this.year=e,this.month=t,this.day=n,this.hour=o,this.minute=i,this.second=r,this.millisecond=a,this.isLeapSecond=u;function j(){s.typeOf.number.greaterThanOrEquals("Year",e,1),s.typeOf.number.lessThanOrEquals("Year",e,9999),s.typeOf.number.greaterThanOrEquals("Month",t,1),s.typeOf.number.lessThanOrEquals("Month",t,12),s.typeOf.number.greaterThanOrEquals("Day",n,1),s.typeOf.number.lessThanOrEquals("Day",n,31),s.typeOf.number.greaterThanOrEquals("Hour",o,0),s.typeOf.number.lessThanOrEquals("Hour",o,23),s.typeOf.number.greaterThanOrEquals("Minute",i,0),s.typeOf.number.lessThanOrEquals("Minute",i,59),s.typeOf.bool("IsLeapSecond",u),s.typeOf.number.greaterThanOrEquals("Second",r,0),s.typeOf.number.lessThanOrEquals("Second",r,u?60:59),s.typeOf.number.greaterThanOrEquals("Millisecond",a,0),s.typeOf.number.lessThan("Millisecond",a,1e3)}function q(){let k=t===2&&ht(e)?jn[t-1]+1:jn[t-1];if(n>k)throw new A("Month and Day represents invalid date")}}var Tt=Go;function Ko(e,t){this.julianDate=e,this.offset=t}var ne=Ko;var er={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},ce=Object.freeze(er);var tr={UTC:0,TAI:1},$=Object.freeze(tr);var Pn=new Tt,vt=[31,28,31,30,31,30,31,31,30,31,30,31],Ct=29;function Kt(e,t){return I.compare(e.julianDate,t.julianDate)}var Ke=new ne;function jt(e){Ke.julianDate=e;let t=I.leapSeconds,n=Be(t,Ke,Kt);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let o=t[n].offset;n>0&&I.secondsDifference(t[n].julianDate,e)>o&&(n--,o=t[n].offset),I.addSeconds(e,o,e)}function Mn(e,t){Ke.julianDate=e;let n=I.leapSeconds,o=Be(n,Ke,Kt);if(o<0&&(o=~o),o===0)return I.addSeconds(e,-n[0].offset,t);if(o>=n.length)return I.addSeconds(e,-n[o-1].offset,t);let i=I.secondsDifference(n[o].julianDate,e);if(i===0)return I.addSeconds(e,-n[o].offset,t);if(!(i<=1))return I.addSeconds(e,-n[--o].offset,t)}function Ne(e,t,n){let o=t/ce.SECONDS_PER_DAY|0;return e+=o,t-=ce.SECONDS_PER_DAY*o,t<0&&(e--,t+=ce.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function en(e,t,n,o,i,r,a){let u=(t-14)/12|0,d=e+4800+u,m=(1461*d/4|0)+(367*(t-2-12*u)/12|0)-(3*((d+100)/100|0)/4|0)+n-32075;o=o-12,o<0&&(o+=24);let l=r+(o*ce.SECONDS_PER_HOUR+i*ce.SECONDS_PER_MINUTE+a*ce.SECONDS_PER_MILLISECOND);return l>=43200&&(m-=1),[m,l]}var nr=/^(\d{4})$/,or=/^(\d{4})-(\d{2})$/,rr=/^(\d{4})-?(\d{3})$/,ir=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,cr=/^(\d{4})-?(\d{2})-?(\d{2})$/,tn=/([Z+\-])?(\d{2})?:?(\d{2})?$/,sr=/^(\d{2})(\.\d+)?/.source+tn.source,ar=/^(\d{2}):?(\d{2})(\.\d+)?/.source+tn.source,fr=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+tn.source,Re="Invalid ISO 8601 date.";function I(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=O(e,0),t=O(t,0),n=O(n,$.UTC);let o=e|0;t=t+(e-o)*ce.SECONDS_PER_DAY,Ne(o,t,this),n===$.UTC&&jt(this)}I.fromGregorianDate=function(e,t){if(!(e instanceof Tt))throw new A("date must be a valid GregorianDate.");let n=en(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return p(t)?(Ne(n[0],n[1],t),jt(t),t):new I(n[0],n[1],$.UTC)};I.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new A("date must be a valid JavaScript Date.");let n=en(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return p(t)?(Ne(n[0],n[1],t),jt(t),t):new I(n[0],n[1],$.UTC)};I.fromIso8601=function(e,t){if(typeof e!="string")throw new A(Re);e=e.replace(",",".");let n=e.split("T"),o,i=1,r=1,a=0,u=0,d=0,m=0,l=n[0],w=n[1],T,v;if(!p(l))throw new A(Re);let P;if(n=l.match(cr),n!==null){if(P=l.split("-").length-1,P>0&&P!==2)throw new A(Re);o=+n[1],i=+n[2],r=+n[3]}else if(n=l.match(or),n!==null)o=+n[1],i=+n[2];else if(n=l.match(nr),n!==null)o=+n[1];else{let x;if(n=l.match(rr),n!==null){if(o=+n[1],x=+n[2],v=ht(o),x<1||v&&x>366||!v&&x>365)throw new A(Re)}else if(n=l.match(ir),n!==null){o=+n[1];let F=+n[2],B=+n[3]||0;if(P=l.split("-").length-1,P>0&&(!p(n[3])&&P!==1||p(n[3])&&P!==2))throw new A(Re);let Q=new Date(Date.UTC(o,0,4));x=F*7+B-Q.getUTCDay()-3}else throw new A(Re);T=new Date(Date.UTC(o,0,1)),T.setUTCDate(x),i=T.getUTCMonth()+1,r=T.getUTCDate()}if(v=ht(o),i<1||i>12||r<1||(i!==2||!v)&&r>vt[i-1]||v&&i===2&&r>Ct)throw new A(Re);let j;if(p(w)){if(n=w.match(fr),n!==null){if(P=w.split(":").length-1,P>0&&P!==2&&P!==3)throw new A(Re);a=+n[1],u=+n[2],d=+n[3],m=+(n[4]||0)*1e3,j=5}else if(n=w.match(ar),n!==null){if(P=w.split(":").length-1,P>2)throw new A(Re);a=+n[1],u=+n[2],d=+(n[3]||0)*60,j=4}else if(n=w.match(sr),n!==null)a=+n[1],u=+(n[2]||0)*60,j=3;else throw new A(Re);if(u>=60||d>=61||a>24||a===24&&(u>0||d>0||m>0))throw new A(Re);let x=n[j],F=+n[j+1],B=+(n[j+2]||0);switch(x){case"+":a=a-F,u=u-B;break;case"-":a=a+F,u=u+B;break;case"Z":break;default:u=u+new Date(Date.UTC(o,i-1,r,a,u)).getTimezoneOffset();break}}let q=d===60;for(q&&d--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,r++;for(T=v&&i===2?Ct:vt[i-1];r>T;)r-=T,i++,i>12&&(i-=12,o++),T=v&&i===2?Ct:vt[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,r--;for(;r<1;)i--,i<1&&(i+=12,o--),T=v&&i===2?Ct:vt[i-1],r+=T;let k=en(o,i,r,a,u,d,m);return p(t)?(Ne(k[0],k[1],t),jt(t)):t=new I(k[0],k[1],$.UTC),q&&I.addSeconds(t,1,t),t};I.now=function(e){return I.fromDate(new Date,e)};var At=new I(0,0,$.TAI);I.toGregorianDate=function(e,t){if(!p(e))throw new A("julianDate is required.");let n=!1,o=Mn(e,At);p(o)||(I.addSeconds(e,-1,At),o=Mn(At,At),n=!0);let i=o.dayNumber,r=o.secondsOfDay;r>=43200&&(i+=1);let a=i+68569|0,u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;let d=4e3*(a+1)/1461001|0;a=a-(1461*d/4|0)+31|0;let m=80*a/2447|0,l=a-(2447*m/80|0)|0;a=m/11|0;let w=m+2-12*a|0,T=100*(u-49)+d+a|0,v=r/ce.SECONDS_PER_HOUR|0,P=r-v*ce.SECONDS_PER_HOUR,j=P/ce.SECONDS_PER_MINUTE|0;P=P-j*ce.SECONDS_PER_MINUTE;let q=P|0,k=(P-q)/ce.SECONDS_PER_MILLISECOND;return v+=12,v>23&&(v-=24),n&&(q+=1),p(t)?(t.year=T,t.month=w,t.day=l,t.hour=v,t.minute=j,t.second=q,t.millisecond=k,t.isLeapSecond=n,t):new Tt(T,w,l,v,j,q,k,n)};I.toDate=function(e){if(!p(e))throw new A("julianDate is required.");let t=I.toGregorianDate(e,Pn),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))};I.toIso8601=function(e,t){if(!p(e))throw new A("julianDate is required.");let n=I.toGregorianDate(e,Pn),o=n.year,i=n.month,r=n.day,a=n.hour,u=n.minute,d=n.second,m=n.millisecond;o===1e4&&i===1&&r===1&&a===0&&u===0&&d===0&&m===0&&(o=9999,i=12,r=31,a=24);let l;return!p(t)&&m!==0?(l=(m*.01).toString().replace(".",""),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`):!p(t)||t===0?`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}Z`:(l=(m*.01).toFixed(t).replace(".","").slice(0,t),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`)};I.clone=function(e,t){if(p(e))return p(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new I(e.dayNumber,e.secondsOfDay,$.TAI)};I.compare=function(e,t){if(!p(e))throw new A("left is required.");if(!p(t))throw new A("right is required.");let n=e.dayNumber-t.dayNumber;return n!==0?n:e.secondsOfDay-t.secondsOfDay};I.equals=function(e,t){return e===t||p(e)&&p(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay};I.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||p(e)&&p(t)&&Math.abs(I.secondsDifference(e,t))<=n};I.totalDays=function(e){if(!p(e))throw new A("julianDate is required.");return e.dayNumber+e.secondsOfDay/ce.SECONDS_PER_DAY};I.secondsDifference=function(e,t){if(!p(e))throw new A("left is required.");if(!p(t))throw new A("right is required.");return(e.dayNumber-t.dayNumber)*ce.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)};I.daysDifference=function(e,t){if(!p(e))throw new A("left is required.");if(!p(t))throw new A("right is required.");let n=e.dayNumber-t.dayNumber,o=(e.secondsOfDay-t.secondsOfDay)/ce.SECONDS_PER_DAY;return n+o};I.computeTaiMinusUtc=function(e){Ke.julianDate=e;let t=I.leapSeconds,n=Be(t,Ke,Kt);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset};I.addSeconds=function(e,t,n){if(!p(e))throw new A("julianDate is required.");if(!p(t))throw new A("seconds is required.");if(!p(n))throw new A("result is required.");return Ne(e.dayNumber,e.secondsOfDay+t,n)};I.addMinutes=function(e,t,n){if(!p(e))throw new A("julianDate is required.");if(!p(t))throw new A("minutes is required.");if(!p(n))throw new A("result is required.");let o=e.secondsOfDay+t*ce.SECONDS_PER_MINUTE;return Ne(e.dayNumber,o,n)};I.addHours=function(e,t,n){if(!p(e))throw new A("julianDate is required.");if(!p(t))throw new A("hours is required.");if(!p(n))throw new A("result is required.");let o=e.secondsOfDay+t*ce.SECONDS_PER_HOUR;return Ne(e.dayNumber,o,n)};I.addDays=function(e,t,n){if(!p(e))throw new A("julianDate is required.");if(!p(t))throw new A("days is required.");if(!p(n))throw new A("result is required.");let o=e.dayNumber+t;return Ne(o,e.secondsOfDay,n)};I.lessThan=function(e,t){return I.compare(e,t)<0};I.lessThanOrEquals=function(e,t){return I.compare(e,t)<=0};I.greaterThan=function(e,t){return I.compare(e,t)>0};I.greaterThanOrEquals=function(e,t){return I.compare(e,t)>=0};I.prototype.clone=function(e){return I.clone(this,e)};I.prototype.equals=function(e){return I.equals(this,e)};I.prototype.equalsEpsilon=function(e,t){return I.equalsEpsilon(this,e,t)};I.prototype.toString=function(){return I.toIso8601(this)};I.leapSeconds=[new ne(new I(2441317,43210,$.TAI),10),new ne(new I(2441499,43211,$.TAI),11),new ne(new I(2441683,43212,$.TAI),12),new ne(new I(2442048,43213,$.TAI),13),new ne(new I(2442413,43214,$.TAI),14),new ne(new I(2442778,43215,$.TAI),15),new ne(new I(2443144,43216,$.TAI),16),new ne(new I(2443509,43217,$.TAI),17),new ne(new I(2443874,43218,$.TAI),18),new ne(new I(2444239,43219,$.TAI),19),new ne(new I(2444786,43220,$.TAI),20),new ne(new I(2445151,43221,$.TAI),21),new ne(new I(2445516,43222,$.TAI),22),new ne(new I(2446247,43223,$.TAI),23),new ne(new I(2447161,43224,$.TAI),24),new ne(new I(2447892,43225,$.TAI),25),new ne(new I(2448257,43226,$.TAI),26),new ne(new I(2448804,43227,$.TAI),27),new ne(new I(2449169,43228,$.TAI),28),new ne(new I(2449534,43229,$.TAI),29),new ne(new I(2450083,43230,$.TAI),30),new ne(new I(2450630,43231,$.TAI),31),new ne(new I(2451179,43232,$.TAI),32),new ne(new I(2453736,43233,$.TAI),33),new ne(new I(2454832,43234,$.TAI),34),new ne(new I(2456109,43235,$.TAI),35),new ne(new I(2457204,43236,$.TAI),36),new ne(new I(2457754,43237,$.TAI),37)];var pe=I;var so=Ze(We(),1);function ur(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var kn=ur;function Fn(e,t){if(e===null||typeof e!="object")return e;t=O(t,!1);let n=new e.constructor;for(let o in e)if(e.hasOwnProperty(o)){let i=e[o];t&&(i=Fn(i,t)),n[o]=i}return n}var nt=Fn;function pr(){let e,t,n=new Promise(function(o,i){e=o,t=i});return{resolve:e,reject:t,promise:n}}var Qe=pr;var xn=Ze(We(),1);function nn(e,t){let n;return typeof document<"u"&&(n=document),nn._implementation(e,t,n)}nn._implementation=function(e,t,n){if(!p(e))throw new A("relative uri is required.");if(!p(t)){if(typeof n>"u")return e;t=O(n.baseURI,n.location.href)}let o=new xn.default(e);return o.scheme()!==""?o.toString():o.absoluteTo(t).toString()};var dt=nn;var Ln=Ze(We(),1);function hr(e,t){if(!p(e))throw new A("uri is required.");let n="",o=e.lastIndexOf("/");return o!==-1&&(n=e.substring(0,o+1)),t&&(e=new Ln.default(e),e.query().length!==0&&(n+=`?${e.query()}`),e.fragment().length!==0&&(n+=`#${e.fragment()}`)),n}var Bn=hr;var Wn=Ze(We(),1);function dr(e){if(!p(e))throw new A("uri is required.");let t=new Wn.default(e);t.normalize();let n=t.path(),o=n.lastIndexOf("/");return o!==-1&&(n=n.substr(o+1)),o=n.lastIndexOf("."),o===-1?n="":n=n.substr(o+1),n}var Qn=dr;var Hn={};function mr(e,t,n){p(t)||(t=e.width),p(n)||(n=e.height);let o=Hn[t];p(o)||(o={},Hn[t]=o);let i=o[n];if(!p(i)){let r=document.createElement("canvas");r.width=t,r.height=n,i=r.getContext("2d",{willReadFrequently:!0}),i.globalCompositeOperation="copy",o[n]=i}return i.drawImage(e,0,0,t,n),i.getImageData(0,0,t,n).data}var on=mr;var yr=/^blob:/i;function lr(e){return s.typeOf.string("uri",e),yr.test(e)}var It=lr;var ve;function wr(e){p(ve)||(ve=document.createElement("a")),ve.href=window.location.href;let t=ve.host,n=ve.protocol;return ve.href=e,ve.href=ve.href,n!==ve.protocol||t!==ve.host}var $n=wr;var br=/^data:/i;function Or(e){return s.typeOf.string("uri",e),br.test(e)}var zt=Or;function gr(e){let t=document.createElement("script");return t.async=!0,t.src=e,new Promise((n,o)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");let i=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,i.removeChild(t),n()},t.onerror=function(r){o(r)},i.appendChild(t)})}var Vn=gr;function _r(e){if(!p(e))throw new A("obj is required.");let t="";for(let n in e)if(e.hasOwnProperty(n)){let o=e[n],i=`${encodeURIComponent(n)}=`;if(Array.isArray(o))for(let r=0,a=o.length;r<a;++r)t+=`${i+encodeURIComponent(o[r])}&`;else t+=`${i+encodeURIComponent(o)}&`}return t=t.slice(0,-1),t}var Yn=_r;function Sr(e){if(!p(e))throw new A("queryString is required.");let t={};if(e==="")return t;let n=e.replace(/\+/g,"%20").split(/[&;]/);for(let o=0,i=n.length;o<i;++o){let r=n[o].split("="),a=decodeURIComponent(r[0]),u=r[1];p(u)?u=decodeURIComponent(u):u="";let d=t[a];typeof d=="string"?t[a]=[d,u]:Array.isArray(d)?d.push(u):t[a]=u}return t}var Xn=Sr;var Rr={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},fe=Object.freeze(Rr);var Er={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},Zn=Object.freeze(Er);function qt(e){e=O(e,O.EMPTY_OBJECT);let t=O(e.throttleByServer,!1),n=O(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=O(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=O(e.type,Zn.OTHER),this.serverKey=e.serverKey,this.state=fe.UNISSUED,this.deferred=void 0,this.cancelled=!1}qt.prototype.cancel=function(){this.cancelled=!0};qt.prototype.clone=function(e){return p(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=fe.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new qt(this)};var Jn=qt;function Tr(e){let t={};if(!e)return t;let n=e.split(`\r
`);for(let o=0;o<n.length;++o){let i=n[o],r=i.indexOf(": ");if(r>0){let a=i.substring(0,r),u=i.substring(r+2);t[a]=u}}return t}var Gn=Tr;function Kn(e,t,n){this.statusCode=e,this.response=t,this.responseHeaders=n,typeof this.responseHeaders=="string"&&(this.responseHeaders=Gn(this.responseHeaders))}Kn.prototype.toString=function(){let e="Request has failed.";return p(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var mt=Kn;var Dt=Ze(We(),1);function yt(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(yt.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});yt.prototype.addEventListener=function(e,t){s.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(t);let n=this;return function(){n.removeEventListener(e,t)}};yt.prototype.removeEventListener=function(e,t){s.typeOf.func("listener",e);let n=this._listeners,o=this._scopes,i=-1;for(let r=0;r<n.length;r++)if(n[r]===e&&o[r]===t){i=r;break}return i!==-1?(this._insideRaiseEvent?(this._toRemove.push(i),n[i]=void 0,o[i]=void 0):(n.splice(i,1),o.splice(i,1)),!0):!1};function vr(e,t){return t-e}yt.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,t=this._listeners,n=this._scopes,o=t.length;for(e=0;e<o;e++){let r=t[e];p(r)&&t[e].apply(n[e],arguments)}let i=this._toRemove;if(o=i.length,o>0){for(i.sort(vr),e=0;e<o;e++){let r=i[e];t.splice(r,1),n.splice(r,1)}i.length=0}this._insideRaiseEvent=!1};var eo=yt;function He(e){s.typeOf.object("options",e),s.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(He.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){s.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let t=this._length;if(e<t){let n=this._array;for(let o=e;o<t;++o)n[o]=void 0;this._length=e,n.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function rn(e,t,n){let o=e[t];e[t]=e[n],e[n]=o}He.prototype.reserve=function(e){e=O(e,this._length),this._array.length=e};He.prototype.heapify=function(e){e=O(e,0);let t=this._length,n=this._comparator,o=this._array,i=-1,r=!0;for(;r;){let a=2*(e+1),u=a-1;u<t&&n(o[u],o[e])<0?i=u:i=e,a<t&&n(o[a],o[i])<0&&(i=a),i!==e?(rn(o,i,e),e=i):r=!1}};He.prototype.resort=function(){let e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)};He.prototype.insert=function(e){s.defined("element",e);let t=this._array,n=this._comparator,o=this._maximumLength,i=this._length++;for(i<t.length?t[i]=e:t.push(e);i!==0;){let a=Math.floor((i-1)/2);if(n(t[i],t[a])<0)rn(t,i,a),i=a;else break}let r;return p(o)&&this._length>o&&(r=t[o],this._length=o),r};He.prototype.pop=function(e){if(e=O(e,0),this._length===0)return;s.typeOf.number.lessThan("index",e,this._length);let t=this._array,n=t[e];return rn(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};var to=He;function Cr(e,t){return e.priority-t.priority}var K={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},ot=20,we=new to({comparator:Cr});we.maximumLength=ot;we.reserve(ot);var Ce=[],Pe={},Ar=typeof document<"u"?new Dt.default(document.location.href):new Dt.default,Nt=new eo;function se(){}se.maximumRequests=50;se.maximumRequestsPerServer=18;se.requestsByServer={};se.throttleRequests=!0;se.debugShowStatistics=!1;se.requestCompletedEvent=Nt;Object.defineProperties(se,{statistics:{get:function(){return K}},priorityHeapLength:{get:function(){return ot},set:function(e){if(e<ot)for(;we.length>e;){let t=we.pop();$e(t)}ot=e,we.maximumLength=e,we.reserve(e)}}});function no(e){p(e.priorityFunction)&&(e.priority=e.priorityFunction())}se.serverHasOpenSlots=function(e,t){t=O(t,1);let n=O(se.requestsByServer[e],se.maximumRequestsPerServer);return Pe[e]+t<=n};se.heapHasOpenSlots=function(e){return we.length+e<=ot};function oo(e){return e.state===fe.UNISSUED&&(e.state=fe.ISSUED,e.deferred=Qe()),e.deferred.promise}function jr(e){return function(t){if(e.state===fe.CANCELLED)return;let n=e.deferred;--K.numberOfActiveRequests,--Pe[e.serverKey],Nt.raiseEvent(),e.state=fe.RECEIVED,e.deferred=void 0,n.resolve(t)}}function Mr(e){return function(t){e.state!==fe.CANCELLED&&(++K.numberOfFailedRequests,--K.numberOfActiveRequests,--Pe[e.serverKey],Nt.raiseEvent(t),e.state=fe.FAILED,e.deferred.reject(t))}}function ro(e){let t=oo(e);return e.state=fe.ACTIVE,Ce.push(e),++K.numberOfActiveRequests,++K.numberOfActiveRequestsEver,++Pe[e.serverKey],e.requestFunction().then(jr(e)).catch(Mr(e)),t}function $e(e){let t=e.state===fe.ACTIVE;if(e.state=fe.CANCELLED,++K.numberOfCancelledRequests,p(e.deferred)){let n=e.deferred;e.deferred=void 0,n.reject()}t&&(--K.numberOfActiveRequests,--Pe[e.serverKey],++K.numberOfCancelledActiveRequests),p(e.cancelFunction)&&e.cancelFunction()}se.update=function(){let e,t,n=0,o=Ce.length;for(e=0;e<o;++e){if(t=Ce[e],t.cancelled&&$e(t),t.state!==fe.ACTIVE){++n;continue}n>0&&(Ce[e-n]=t)}Ce.length-=n;let i=we.internalArray,r=we.length;for(e=0;e<r;++e)no(i[e]);we.resort();let a=Math.max(se.maximumRequests-Ce.length,0),u=0;for(;u<a&&we.length>0;){if(t=we.pop(),t.cancelled){$e(t);continue}if(t.throttleByServer&&!se.serverHasOpenSlots(t.serverKey)){$e(t);continue}ro(t),++u}Pr()};se.getServerKey=function(e){s.typeOf.string("url",e);let t=new Dt.default(e);t.scheme()===""&&(t=t.absoluteTo(Ar),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${t.scheme()==="https"?"443":"80"}`);let o=Pe[n];return p(o)||(Pe[n]=0),n};se.request=function(e){if(s.typeOf.object("request",e),s.typeOf.string("request.url",e.url),s.typeOf.func("request.requestFunction",e.requestFunction),zt(e.url)||It(e.url))return Nt.raiseEvent(),e.state=fe.RECEIVED,e.requestFunction();if(++K.numberOfAttemptedRequests,p(e.serverKey)||(e.serverKey=se.getServerKey(e.url)),se.throttleRequests&&e.throttleByServer&&!se.serverHasOpenSlots(e.serverKey))return;if(!se.throttleRequests||!e.throttle)return ro(e);if(Ce.length>=se.maximumRequests)return;no(e);let t=we.insert(e);if(p(t)){if(t===e)return;$e(t)}return oo(e)};function Pr(){se.debugShowStatistics&&(K.numberOfActiveRequests===0&&K.lastNumberOfActiveRequests>0&&(K.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${K.numberOfAttemptedRequests}`),K.numberOfAttemptedRequests=0),K.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${K.numberOfCancelledRequests}`),K.numberOfCancelledRequests=0),K.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${K.numberOfCancelledActiveRequests}`),K.numberOfCancelledActiveRequests=0),K.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${K.numberOfFailedRequests}`),K.numberOfFailedRequests=0)),K.lastNumberOfActiveRequests=K.numberOfActiveRequests)}se.clearForSpecs=function(){for(;we.length>0;){let t=we.pop();$e(t)}let e=Ce.length;for(let t=0;t<e;++t)$e(Ce[t]);Ce.length=0,Pe={},K.numberOfAttemptedRequests=0,K.numberOfActiveRequests=0,K.numberOfCancelledRequests=0,K.numberOfCancelledActiveRequests=0,K.numberOfFailedRequests=0,K.numberOfActiveRequestsEver=0,K.lastNumberOfActiveRequests=0};se.numberOfActiveRequestsByServer=function(e){return Pe[e]};se.requestHeap=we;var kt=se;var io=Ze(We(),1);var lt={},rt={};lt.add=function(e,t){if(!p(e))throw new A("host is required.");if(!p(t)||t<=0)throw new A("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;p(rt[n])||(rt[n]=!0)};lt.remove=function(e,t){if(!p(e))throw new A("host is required.");if(!p(t)||t<=0)throw new A("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;p(rt[n])&&delete rt[n]};function Ur(e){let t=new io.default(e);t.normalize();let n=t.authority();if(n.length!==0){if(t.authority(n),n.indexOf("@")!==-1&&(n=n.split("@")[1]),n.indexOf(":")===-1){let o=t.scheme();if(o.length===0&&(o=window.location.protocol,o=o.substring(0,o.length-1)),o==="http")n+=":80";else if(o==="https")n+=":443";else return}return n}}lt.contains=function(e){if(!p(e))throw new A("url is required.");let t=Ur(e);return!!(p(t)&&p(rt[t]))};lt.clear=function(){rt={}};var cn=lt;var ao=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function U(e){e=O(e,O.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),s.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Ae(e.templateValues,{}),this._queryParameters=Ae(e.queryParameters,{}),this.headers=Ae(e.headers,{}),this.request=O(e.request,new Jn),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=O(e.retryAttempts,0),this._retryCount=0,O(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Ae(e,t){return p(e)?nt(e):t}U.createIfNeeded=function(e){return e instanceof U?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new U({url:e})};var it;U.supportsImageBitmapOptions=function(){return p(it)?it:typeof createImageBitmap!="function"?(it=Promise.resolve(!1),it):(it=U.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(t){let n={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(t,n),createImageBitmap(t)])}).then(function(t){let n=on(t[0]),o=on(t[1]);return n[1]!==o[1]}).catch(function(){return!1}),it)};Object.defineProperties(U,{isBlobSupported:{get:function(){return ao}}});Object.defineProperties(U.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return Qn(this._url)}},isDataUri:{get:function(){return zt(this._url)}},isBlobUri:{get:function(){return It(this._url)}},isCrossOriginUrl:{get:function(){return $n(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});U.prototype.toString=function(){return this.getUrlComponent(!0,!0)};U.prototype.parseUrl=function(e,t,n,o){let i=new so.default(e),r=Ir(i.query());this._queryParameters=t?xt(r,this.queryParameters,n):r,i.search(""),i.fragment(""),p(o)&&i.scheme()===""&&(i=i.absoluteTo(dt(o))),this._url=i.toString()};function Ir(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Xn(e)}function xt(e,t,n){if(!n)return De(e,t);let o=nt(e,!0);for(let i in t)if(t.hasOwnProperty(i)){let r=o[i],a=t[i];p(r)?(Array.isArray(r)||(r=o[i]=[r]),o[i]=r.concat(a)):o[i]=Array.isArray(a)?a.slice():a}return o}U.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;let n=this._url;e&&(n=`${n}${zr(this.queryParameters)}`),n=n.replace(/%7B/g,"{").replace(/%7D/g,"}");let o=this._templateValues;return Object.keys(o).length>0&&(n=n.replace(/{(.*?)}/g,function(i,r){let a=o[r];return p(a)?encodeURIComponent(a):i})),t&&p(this.proxy)&&(n=this.proxy.getURL(n)),n};function zr(e){let t=Object.keys(e);return t.length===0?"":t.length===1&&!p(e[t[0]])?`?${t[0]}`:`?${Yn(e)}`}U.prototype.setQueryParameters=function(e,t){t?this._queryParameters=xt(this._queryParameters,e,!1):this._queryParameters=xt(e,this._queryParameters,!1)};U.prototype.appendQueryParameters=function(e){this._queryParameters=xt(e,this._queryParameters,!0)};U.prototype.setTemplateValues=function(e,t){t?this._templateValues=De(this._templateValues,e):this._templateValues=De(e,this._templateValues)};U.prototype.getDerivedResource=function(e){let t=this.clone();if(t._retryCount=0,p(e.url)){let n=O(e.preserveQueryParameters,!1);t.parseUrl(e.url,!0,n,this._url)}return p(e.queryParameters)&&(t._queryParameters=De(e.queryParameters,t.queryParameters)),p(e.templateValues)&&(t._templateValues=De(e.templateValues,t.templateValues)),p(e.headers)&&(t.headers=De(e.headers,t.headers)),p(e.proxy)&&(t.proxy=e.proxy),p(e.request)&&(t.request=e.request),p(e.retryCallback)&&(t.retryCallback=e.retryCallback),p(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t};U.prototype.retryOnError=function(e){let t=this.retryCallback;if(typeof t!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let n=this;return Promise.resolve(t(this,e)).then(function(o){return++n._retryCount,o})};U.prototype.clone=function(e){return p(e)?(e._url=this._url,e._queryParameters=nt(this._queryParameters),e._templateValues=nt(this._templateValues),e.headers=nt(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new U({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:p(this.credits)?this.credits.slice():void 0})};U.prototype.getBaseUri=function(e){return Bn(this.getUrlComponent(e),e)};U.prototype.appendForwardSlash=function(){this._url=kn(this._url)};U.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};U.fetchArrayBuffer=function(e){return new U(e).fetchArrayBuffer()};U.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};U.fetchBlob=function(e){return new U(e).fetchBlob()};U.prototype.fetchImage=function(e){e=O(e,O.EMPTY_OBJECT);let t=O(e.preferImageBitmap,!1),n=O(e.preferBlob,!1),o=O(e.flipY,!1),i=O(e.skipColorSpaceConversion,!1);if(an(this.request),!ao||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return sn({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:t});let r=this.fetchBlob();if(!p(r))return;let a,u,d,m;return U.supportsImageBitmapOptions().then(function(l){return a=l,u=a&&t,r}).then(function(l){if(!p(l))return;if(m=l,u)return U.createImageBitmapFromBlob(l,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});let w=window.URL.createObjectURL(l);return d=new U({url:w}),sn({resource:d,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})}).then(function(l){if(p(l))return l.blob=m,u||window.URL.revokeObjectURL(d.url),l}).catch(function(l){return p(d)&&window.URL.revokeObjectURL(d.url),l.blob=m,Promise.reject(l)})};function sn(e){let t=e.resource,n=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,r=t.request;r.url=t.url,r.requestFunction=function(){let u=!1;!t.isDataUri&&!t.isBlobUri&&(u=t.isCrossOriginUrl);let d=Qe();return U._Implementations.createImage(r,u,d,n,o,i),d.promise};let a=kt.request(r);if(p(a))return a.catch(function(u){return r.state!==fe.FAILED?Promise.reject(u):t.retryOnError(u).then(function(d){return d?(r.state=fe.UNISSUED,r.deferred=void 0,sn({resource:t,flipY:n,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(u)})})}U.fetchImage=function(e){return new U(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};U.prototype.fetchText=function(){return this.fetch({responseType:"text"})};U.fetchText=function(e){return new U(e).fetchText()};U.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(p(e))return e.then(function(t){if(p(t))return JSON.parse(t)})};U.fetchJson=function(e){return new U(e).fetchJson()};U.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};U.fetchXML=function(e){return new U(e).fetchXML()};U.prototype.fetchJsonp=function(e){e=O(e,"callback"),an(this.request);let t;do t=`loadJsonp${E.nextRandomNumber().toString().substring(2,8)}`;while(p(window[t]));return fo(this,e,t)};function fo(e,t,n){let o={};o[t]=n,e.setQueryParameters(o);let i=e.request,r=e.url;i.url=r,i.requestFunction=function(){let u=Qe();return window[n]=function(d){u.resolve(d);try{delete window[n]}catch{window[n]=void 0}},U._Implementations.loadAndExecuteScript(r,n,u),u.promise};let a=kt.request(i);if(p(a))return a.catch(function(u){return i.state!==fe.FAILED?Promise.reject(u):e.retryOnError(u).then(function(d){return d?(i.state=fe.UNISSUED,i.deferred=void 0,fo(e,t,n)):Promise.reject(u)})})}U.fetchJsonp=function(e){return new U(e).fetchJsonp(e.callbackParameterName)};U.prototype._makeRequest=function(e){let t=this;an(t.request);let n=t.request,o=t.url;n.url=o,n.requestFunction=function(){let r=e.responseType,a=De(e.headers,t.headers),u=e.overrideMimeType,d=e.method,m=e.data,l=Qe(),w=U._Implementations.loadWithXhr(o,r,d,m,a,l,u);return p(w)&&p(w.abort)&&(n.cancelFunction=function(){w.abort()}),l.promise};let i=kt.request(n);if(p(i))return i.then(function(r){return n.cancelFunction=void 0,r}).catch(function(r){return n.cancelFunction=void 0,n.state!==fe.FAILED?Promise.reject(r):t.retryOnError(r).then(function(a){return a?(n.state=fe.UNISSUED,n.deferred=void 0,t.fetch(e)):Promise.reject(r)})})};function an(e){if(e.state===fe.ISSUED||e.state===fe.ACTIVE)throw new Se("The Resource is already being fetched.");e.state=fe.UNISSUED,e.deferred=void 0}var qr=/^data:(.*?)(;base64)?,(.*)$/;function Ft(e,t){let n=decodeURIComponent(t);return e?atob(n):n}function co(e,t){let n=Ft(e,t),o=new ArrayBuffer(n.length),i=new Uint8Array(o);for(let r=0;r<n.length;r++)i[r]=n.charCodeAt(r);return o}function Dr(e,t){t=O(t,"");let n=e[1],o=!!e[2],i=e[3],r,a;switch(t){case"":case"text":return Ft(o,i);case"arraybuffer":return co(o,i);case"blob":return r=co(o,i),new Blob([r],{type:n});case"document":return a=new DOMParser,a.parseFromString(Ft(o,i),n);case"json":return JSON.parse(Ft(o,i));default:throw new A(`Unhandled responseType: ${t}`)}}U.prototype.fetch=function(e){return e=Ae(e,{}),e.method="GET",this._makeRequest(e)};U.fetch=function(e){return new U(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.delete=function(e){return e=Ae(e,{}),e.method="DELETE",this._makeRequest(e)};U.delete=function(e){return new U(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};U.prototype.head=function(e){return e=Ae(e,{}),e.method="HEAD",this._makeRequest(e)};U.head=function(e){return new U(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.options=function(e){return e=Ae(e,{}),e.method="OPTIONS",this._makeRequest(e)};U.options=function(e){return new U(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.post=function(e,t){return s.defined("data",e),t=Ae(t,{}),t.method="POST",t.data=e,this._makeRequest(t)};U.post=function(e){return new U(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.put=function(e,t){return s.defined("data",e),t=Ae(t,{}),t.method="PUT",t.data=e,this._makeRequest(t)};U.put=function(e){return new U(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.patch=function(e,t){return s.defined("data",e),t=Ae(t,{}),t.method="PATCH",t.data=e,this._makeRequest(t)};U.patch=function(e){return new U(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U._Implementations={};U._Implementations.loadImageElement=function(e,t,n){let o=new Image;o.onload=function(){o.naturalWidth===0&&o.naturalHeight===0&&o.width===0&&o.height===0&&(o.width=300,o.height=150),n.resolve(o)},o.onerror=function(i){n.reject(i)},t&&(cn.contains(e)?o.crossOrigin="use-credentials":o.crossOrigin=""),o.src=e};U._Implementations.createImage=function(e,t,n,o,i,r){let a=e.url;U.supportsImageBitmapOptions().then(function(u){if(!(u&&r)){U._Implementations.loadImageElement(a,t,n);return}let d="blob",m="GET",l=Qe(),w=U._Implementations.loadWithXhr(a,d,m,void 0,void 0,l,void 0,void 0,void 0);return p(w)&&p(w.abort)&&(e.cancelFunction=function(){w.abort()}),l.promise.then(function(T){if(!p(T)){n.reject(new Se(`Successfully retrieved ${a} but it contained no content.`));return}return U.createImageBitmapFromBlob(T,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i})}).then(function(T){n.resolve(T)})}).catch(function(u){n.reject(u)})};U.createImageBitmapFromBlob=function(e,t){return s.defined("options",t),s.typeOf.bool("options.flipY",t.flipY),s.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),s.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};function Nr(e,t,n,o,i,r,a){fetch(e,{method:n,headers:i}).then(async u=>{if(!u.ok){let d={};u.headers.forEach((m,l)=>{d[l]=m}),r.reject(new mt(u.status,u,d));return}switch(t){case"text":r.resolve(u.text());break;case"json":r.resolve(u.json());break;default:r.resolve(new Uint8Array(await u.arrayBuffer()).buffer);break}}).catch(()=>{r.reject(new mt)})}var kr=typeof XMLHttpRequest>"u";U._Implementations.loadWithXhr=function(e,t,n,o,i,r,a){let u=qr.exec(e);if(u!==null){r.resolve(Dr(u,t));return}if(kr){Nr(e,t,n,o,i,r,a);return}let d=new XMLHttpRequest;if(cn.contains(e)&&(d.withCredentials=!0),d.open(n,e,!0),p(a)&&p(d.overrideMimeType)&&d.overrideMimeType(a),p(i))for(let l in i)i.hasOwnProperty(l)&&d.setRequestHeader(l,i[l]);p(t)&&(d.responseType=t);let m=!1;return typeof e=="string"&&(m=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),d.onload=function(){if((d.status<200||d.status>=300)&&!(m&&d.status===0)){r.reject(new mt(d.status,d.response,d.getAllResponseHeaders()));return}let l=d.response,w=d.responseType;if(n==="HEAD"||n==="OPTIONS"){let v=d.getAllResponseHeaders().trim().split(/[\r\n]+/),P={};v.forEach(function(j){let q=j.split(": "),k=q.shift();P[k]=q.join(": ")}),r.resolve(P);return}if(d.status===204)r.resolve(void 0);else if(p(l)&&(!p(t)||w===t))r.resolve(l);else if(t==="json"&&typeof l=="string")try{r.resolve(JSON.parse(l))}catch(T){r.reject(T)}else(w===""||w==="document")&&p(d.responseXML)&&d.responseXML.hasChildNodes()?r.resolve(d.responseXML):(w===""||w==="text")&&p(d.responseText)?r.resolve(d.responseText):r.reject(new Se("Invalid XMLHttpRequest response type."))},d.onerror=function(l){r.reject(new mt)},d.send(o),d};U._Implementations.loadAndExecuteScript=function(e,t,n){return Vn(e,t).catch(function(o){n.reject(o)})};U._DefaultImplementations={};U._DefaultImplementations.createImage=U._Implementations.createImage;U._DefaultImplementations.loadWithXhr=U._Implementations.loadWithXhr;U._DefaultImplementations.loadAndExecuteScript=U._Implementations.loadAndExecuteScript;U.DEFAULT=Object.freeze(new U({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var ke=U;function bt(e){e=O(e,O.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=O(e.addNewLeapSeconds,!0),p(e.data)?uo(this,e.data):uo(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}bt.fromUrl=async function(e,t){s.defined("url",e),t=O(t,O.EMPTY_OBJECT);let n=ke.createIfNeeded(e),o;try{o=await n.fetchJson()}catch{throw new Se(`An error occurred while retrieving the EOP data from the URL ${n.url}.`)}return new bt({addNewLeapSeconds:t.addNewLeapSeconds,data:o})};bt.NONE=Object.freeze({compute:function(e,t){return p(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new pt(0,0,0,0,0),t}});bt.prototype.compute=function(e,t){if(!p(this._samples))return;if(p(t)||(t=new pt(0,0,0,0,0)),this._samples.length===0)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;let n=this._dates,o=this._lastIndex,i=0,r=0;if(p(o)){let u=n[o],d=n[o+1],m=pe.lessThanOrEquals(u,e),l=!p(d),w=l||pe.greaterThanOrEquals(d,e);if(m&&w)return i=o,!l&&d.equals(e)&&++i,r=i+1,ho(this,n,this._samples,e,i,r,t),t}let a=Be(n,e,pe.compare,this._dateColumn);return a>=0?(a<n.length-1&&n[a+1].equals(e)&&++a,i=a,r=a):(r=~a,i=r-1,i<0&&(i=0)),this._lastIndex=i,ho(this,n,this._samples,e,i,r,t),t};function Fr(e,t){return pe.compare(e.julianDate,t)}function uo(e,t){if(!p(t.columnNames))throw new Se("Error in loaded EOP data: The columnNames property is required.");if(!p(t.samples))throw new Se("Error in loaded EOP data: The samples property is required.");let n=t.columnNames.indexOf("modifiedJulianDateUtc"),o=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),r=t.columnNames.indexOf("ut1MinusUtcSeconds"),a=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),d=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||o<0||i<0||r<0||a<0||u<0||d<0)throw new Se("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let m=e._samples=t.samples,l=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=r,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=d,e._columnCount=t.columnNames.length,e._lastIndex=void 0;let w,T=e._addNewLeapSeconds;for(let v=0,P=m.length;v<P;v+=e._columnCount){let j=m[v+n],q=m[v+d],k=j+ce.MODIFIED_JULIAN_DATE_DIFFERENCE,x=new pe(k,q,$.TAI);if(l.push(x),T){if(q!==w&&p(w)){let F=pe.leapSeconds,B=Be(F,x,Fr);if(B<0){let Q=new ne(x,q);F.splice(~B,0,Q)}}w=q}}}function po(e,t,n,o,i){let r=n*o;i.xPoleWander=t[r+e._xPoleWanderRadiansColumn],i.yPoleWander=t[r+e._yPoleWanderRadiansColumn],i.xPoleOffset=t[r+e._xCelestialPoleOffsetRadiansColumn],i.yPoleOffset=t[r+e._yCelestialPoleOffsetRadiansColumn],i.ut1MinusUtc=t[r+e._ut1MinusUtcSecondsColumn]}function wt(e,t,n){return t+e*(n-t)}function ho(e,t,n,o,i,r,a){let u=e._columnCount;if(r>t.length-1)return a.xPoleWander=0,a.yPoleWander=0,a.xPoleOffset=0,a.yPoleOffset=0,a.ut1MinusUtc=0,a;let d=t[i],m=t[r];if(d.equals(m)||o.equals(d))return po(e,n,i,u,a),a;if(o.equals(m))return po(e,n,r,u,a),a;let l=pe.secondsDifference(o,d)/pe.secondsDifference(m,d),w=i*u,T=r*u,v=n[w+e._ut1MinusUtcSecondsColumn],P=n[T+e._ut1MinusUtcSecondsColumn],j=P-v;if(j>.5||j<-.5){let q=n[w+e._taiMinusUtcSecondsColumn],k=n[T+e._taiMinusUtcSecondsColumn];q!==k&&(m.equals(o)?v=P:P-=k-q)}return a.xPoleWander=wt(l,n[w+e._xPoleWanderRadiansColumn],n[T+e._xPoleWanderRadiansColumn]),a.yPoleWander=wt(l,n[w+e._yPoleWanderRadiansColumn],n[T+e._yPoleWanderRadiansColumn]),a.xPoleOffset=wt(l,n[w+e._xCelestialPoleOffsetRadiansColumn],n[T+e._xCelestialPoleOffsetRadiansColumn]),a.yPoleOffset=wt(l,n[w+e._yCelestialPoleOffsetRadiansColumn],n[T+e._yCelestialPoleOffsetRadiansColumn]),a.ut1MinusUtc=wt(l,v,P),a}var mo=bt;function be(e,t,n){this.heading=O(e,0),this.pitch=O(t,0),this.roll=O(n,0)}be.fromQuaternion=function(e,t){if(!p(e))throw new A("quaternion is required");p(t)||(t=new be);let n=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),r=1-2*(e.y*e.y+e.z*e.z),a=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(a,r),t.roll=Math.atan2(i,o),t.pitch=-E.asinClamped(n),t};be.fromDegrees=function(e,t,n,o){if(!p(e))throw new A("heading is required");if(!p(t))throw new A("pitch is required");if(!p(n))throw new A("roll is required");return p(o)||(o=new be),o.heading=e*E.RADIANS_PER_DEGREE,o.pitch=t*E.RADIANS_PER_DEGREE,o.roll=n*E.RADIANS_PER_DEGREE,o};be.clone=function(e,t){if(p(e))return p(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new be(e.heading,e.pitch,e.roll)};be.equals=function(e,t){return e===t||p(e)&&p(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll};be.equalsEpsilon=function(e,t,n,o){return e===t||p(e)&&p(t)&&E.equalsEpsilon(e.heading,t.heading,n,o)&&E.equalsEpsilon(e.pitch,t.pitch,n,o)&&E.equalsEpsilon(e.roll,t.roll,n,o)};be.prototype.clone=function(e){return be.clone(this,e)};be.prototype.equals=function(e){return be.equals(this,e)};be.prototype.equalsEpsilon=function(e,t,n){return be.equalsEpsilon(this,e,t,n)};be.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var Lt=be;var yo=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function xr(){let e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){let o=e[t].getAttribute("src"),i=yo.exec(o);if(i!==null)return i[1]}}var Bt;function lo(e){return typeof document>"u"?e:(p(Bt)||(Bt=document.createElement("a")),Bt.href=e,Bt.href)}var Ve;function wo(){if(p(Ve))return Ve;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:p(import.meta?.url)?e=dt(".",import.meta.url):typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(St.toUrl)?e=dt("..",Ye("Core/buildModuleUrl.js")):e=xr(),!p(e))throw new A("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Ve=new ke({url:lo(e)}),Ve.appendForwardSlash(),Ve}function Lr(e){return lo(St.toUrl(`../${e}`))}function bo(e){return wo().getDerivedResource({url:e}).url}var Wt;function Ye(e){return p(Wt)||(typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(St.toUrl)?Wt=Lr:Wt=bo),Wt(e)}Ye._cesiumScriptRegex=yo;Ye._buildModuleUrlFromBaseUrl=bo;Ye._clearBaseResource=function(){Ve=void 0};Ye.setBaseUrl=function(e){Ve=ke.DEFAULT.getDerivedResource({url:e})};Ye.getCesiumBaseUrl=wo;var Oo=Ye;function Br(e,t,n){this.x=e,this.y=t,this.s=n}var Qt=Br;function pn(e){e=O(e,O.EMPTY_OBJECT),this._xysFileUrlTemplate=ke.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=O(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=O(e.sampleZeroJulianEphemerisDate,24423965e-1),this._sampleZeroDateTT=new pe(this._sampleZeroJulianEphemerisDate,0,$.TAI),this._stepSizeDays=O(e.stepSizeDays,1),this._samplesPerXysFile=O(e.samplesPerXysFile,1e3),this._totalSamples=O(e.totalSamples,27426),this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let t=this._interpolationOrder,n=this._denominators=new Array(t+1),o=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t);for(let r=0;r<=t;++r){n[r]=i,o[r]=r*this._stepSizeDays;for(let a=0;a<=t;++a)a!==r&&(n[r]*=r-a);n[r]=1/n[r]}this._work=new Array(t+1),this._coef=new Array(t+1)}var Wr=new pe(0,0,$.TAI);function fn(e,t,n){let o=Wr;return o.dayNumber=t,o.secondsOfDay=n,pe.daysDifference(o,e._sampleZeroDateTT)}pn.prototype.preload=function(e,t,n,o){let i=fn(this,e,t),r=fn(this,n,o),a=i/this._stepSizeDays-this._interpolationOrder/2|0;a<0&&(a=0);let u=r/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let d=a/this._samplesPerXysFile|0,m=u/this._samplesPerXysFile|0,l=[];for(let w=d;w<=m;++w)l.push(un(this,w));return Promise.all(l)};pn.prototype.computeXysRadians=function(e,t,n){let o=fn(this,e,t);if(o<0)return;let i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;let r=this._interpolationOrder,a=i-(r/2|0);a<0&&(a=0);let u=a+r;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-r,a<0&&(a=0));let d=!1,m=this._samples;if(p(m[a*3])||(un(this,a/this._samplesPerXysFile|0),d=!0),p(m[u*3])||(un(this,u/this._samplesPerXysFile|0),d=!0),d)return;p(n)?(n.x=0,n.y=0,n.s=0):n=new Qt(0,0,0);let l=o-a*this._stepSizeDays,w=this._work,T=this._denominators,v=this._coef,P=this._xTable,j,q;for(j=0;j<=r;++j)w[j]=l-P[j];for(j=0;j<=r;++j){for(v[j]=1,q=0;q<=r;++q)q!==j&&(v[j]*=w[q]);v[j]*=T[j];let k=(a+j)*3;n.x+=v[j]*m[k++],n.y+=v[j]*m[k++],n.s+=v[j]*m[k]}return n};function un(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n,o=e._xysFileUrlTemplate;p(o)?n=o.getDerivedResource({templateValues:{0:t}}):n=new ke({url:Oo(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});let i=n.fetchJson().then(function(r){e._chunkDownloadsInProgress[t]=!1;let a=e._samples,u=r.samples,d=t*e._samplesPerXysFile*3;for(let m=0,l=u.length;m<l;++m)a[d+m]=u[m]});return e._chunkDownloadsInProgress[t]=i,i}var go=pn;function R(e,t,n,o){this.x=O(e,0),this.y=O(t,0),this.z=O(n,0),this.w=O(o,0)}var Ot=new _;R.fromAxisAngle=function(e,t,n){s.typeOf.object("axis",e),s.typeOf.number("angle",t);let o=t/2,i=Math.sin(o);Ot=_.normalize(e,Ot);let r=Ot.x*i,a=Ot.y*i,u=Ot.z*i,d=Math.cos(o);return p(n)?(n.x=r,n.y=a,n.z=u,n.w=d,n):new R(r,a,u,d)};var Qr=[1,2,0],Hr=new Array(3);R.fromRotationMatrix=function(e,t){s.typeOf.object("matrix",e);let n,o,i,r,a,u=e[W.COLUMN0ROW0],d=e[W.COLUMN1ROW1],m=e[W.COLUMN2ROW2],l=u+d+m;if(l>0)n=Math.sqrt(l+1),a=.5*n,n=.5/n,o=(e[W.COLUMN1ROW2]-e[W.COLUMN2ROW1])*n,i=(e[W.COLUMN2ROW0]-e[W.COLUMN0ROW2])*n,r=(e[W.COLUMN0ROW1]-e[W.COLUMN1ROW0])*n;else{let w=Qr,T=0;d>u&&(T=1),m>u&&m>d&&(T=2);let v=w[T],P=w[v];n=Math.sqrt(e[W.getElementIndex(T,T)]-e[W.getElementIndex(v,v)]-e[W.getElementIndex(P,P)]+1);let j=Hr;j[T]=.5*n,n=.5/n,a=(e[W.getElementIndex(P,v)]-e[W.getElementIndex(v,P)])*n,j[v]=(e[W.getElementIndex(v,T)]+e[W.getElementIndex(T,v)])*n,j[P]=(e[W.getElementIndex(P,T)]+e[W.getElementIndex(T,P)])*n,o=-j[0],i=-j[1],r=-j[2]}return p(t)?(t.x=o,t.y=i,t.z=r,t.w=a,t):new R(o,i,r,a)};var _o=new R,So=new R,hn=new R,Ro=new R;R.fromHeadingPitchRoll=function(e,t){return s.typeOf.object("headingPitchRoll",e),Ro=R.fromAxisAngle(_.UNIT_X,e.roll,_o),hn=R.fromAxisAngle(_.UNIT_Y,-e.pitch,t),t=R.multiply(hn,Ro,hn),So=R.fromAxisAngle(_.UNIT_Z,-e.heading,_o),R.multiply(So,t,t)};var Ht=new _,dn=new _,Ee=new R,Eo=new R,$t=new R;R.packedLength=4;R.pack=function(e,t,n){return s.typeOf.object("value",e),s.defined("array",t),n=O(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t};R.unpack=function(e,t,n){return s.defined("array",e),t=O(t,0),p(n)||(n=new R),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n};R.packedInterpolationLength=3;R.convertPackedArrayForInterpolation=function(e,t,n,o){R.unpack(e,n*4,$t),R.conjugate($t,$t);for(let i=0,r=n-t+1;i<r;i++){let a=i*3;R.unpack(e,(t+i)*4,Ee),R.multiply(Ee,$t,Ee),Ee.w<0&&R.negate(Ee,Ee),R.computeAxis(Ee,Ht);let u=R.computeAngle(Ee);p(o)||(o=[]),o[a]=Ht.x*u,o[a+1]=Ht.y*u,o[a+2]=Ht.z*u}};R.unpackInterpolationResult=function(e,t,n,o,i){p(i)||(i=new R),_.fromArray(e,0,dn);let r=_.magnitude(dn);return R.unpack(t,o*4,Eo),r===0?R.clone(R.IDENTITY,Ee):R.fromAxisAngle(dn,r,Ee),R.multiply(Ee,Eo,i)};R.clone=function(e,t){if(p(e))return p(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new R(e.x,e.y,e.z,e.w)};R.conjugate=function(e,t){return s.typeOf.object("quaternion",e),s.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t};R.magnitudeSquared=function(e){return s.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};R.magnitude=function(e){return Math.sqrt(R.magnitudeSquared(e))};R.normalize=function(e,t){s.typeOf.object("result",t);let n=1/R.magnitude(e),o=e.x*n,i=e.y*n,r=e.z*n,a=e.w*n;return t.x=o,t.y=i,t.z=r,t.w=a,t};R.inverse=function(e,t){s.typeOf.object("result",t);let n=R.magnitudeSquared(e);return t=R.conjugate(e,t),R.multiplyByScalar(t,1/n,t)};R.add=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n};R.subtract=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n};R.negate=function(e,t){return s.typeOf.object("quaternion",e),s.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t};R.dot=function(e,t){return s.typeOf.object("left",e),s.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w};R.multiply=function(e,t,n){s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n);let o=e.x,i=e.y,r=e.z,a=e.w,u=t.x,d=t.y,m=t.z,l=t.w,w=a*u+o*l+i*m-r*d,T=a*d-o*m+i*l+r*u,v=a*m+o*d-i*u+r*l,P=a*l-o*u-i*d-r*m;return n.x=w,n.y=T,n.z=v,n.w=P,n};R.multiplyByScalar=function(e,t,n){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n};R.divideByScalar=function(e,t,n){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n};R.computeAxis=function(e,t){s.typeOf.object("quaternion",e),s.typeOf.object("result",t);let n=e.w;if(Math.abs(n-1)<E.EPSILON6||Math.abs(n+1)<E.EPSILON6)return t.x=1,t.y=t.z=0,t;let o=1/Math.sqrt(1-n*n);return t.x=e.x*o,t.y=e.y*o,t.z=e.z*o,t};R.computeAngle=function(e){return s.typeOf.object("quaternion",e),Math.abs(e.w-1)<E.EPSILON6?0:2*Math.acos(e.w)};var mn=new R;R.lerp=function(e,t,n,o){return s.typeOf.object("start",e),s.typeOf.object("end",t),s.typeOf.number("t",n),s.typeOf.object("result",o),mn=R.multiplyByScalar(t,n,mn),o=R.multiplyByScalar(e,1-n,o),R.add(mn,o,o)};var To=new R,yn=new R,ln=new R;R.slerp=function(e,t,n,o){s.typeOf.object("start",e),s.typeOf.object("end",t),s.typeOf.number("t",n),s.typeOf.object("result",o);let i=R.dot(e,t),r=t;if(i<0&&(i=-i,r=To=R.negate(t,To)),1-i<E.EPSILON6)return R.lerp(e,r,n,o);let a=Math.acos(i);return yn=R.multiplyByScalar(e,Math.sin((1-n)*a),yn),ln=R.multiplyByScalar(r,Math.sin(n*a),ln),o=R.add(yn,ln,o),R.multiplyByScalar(o,1/Math.sin(a),o)};R.log=function(e,t){s.typeOf.object("quaternion",e),s.typeOf.object("result",t);let n=E.acosClamped(e.w),o=0;return n!==0&&(o=n/Math.sin(n)),_.multiplyByScalar(e,o,t)};R.exp=function(e,t){s.typeOf.object("cartesian",e),s.typeOf.object("result",t);let n=_.magnitude(e),o=0;return n!==0&&(o=Math.sin(n)/n),t.x=e.x*o,t.y=e.y*o,t.z=e.z*o,t.w=Math.cos(n),t};var $r=new _,Vr=new _,gt=new R,ct=new R;R.computeInnerQuadrangle=function(e,t,n,o){s.typeOf.object("q0",e),s.typeOf.object("q1",t),s.typeOf.object("q2",n),s.typeOf.object("result",o);let i=R.conjugate(t,gt);R.multiply(i,n,ct);let r=R.log(ct,$r);R.multiply(i,e,ct);let a=R.log(ct,Vr);return _.add(r,a,r),_.multiplyByScalar(r,.25,r),_.negate(r,r),R.exp(r,gt),R.multiply(t,gt,o)};R.squad=function(e,t,n,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",t),s.typeOf.object("s0",n),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=R.slerp(e,t,i,gt),u=R.slerp(n,o,i,ct);return R.slerp(a,u,2*i*(1-i),r)};var Yr=new R,vo=1.9011074535173003,Vt=ft.supportsTypedArrays()?new Float32Array(8):[],Yt=ft.supportsTypedArrays()?new Float32Array(8):[],Ue=ft.supportsTypedArrays()?new Float32Array(8):[],Ie=ft.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let t=e+1,n=2*t+1;Vt[e]=1/(t*n),Yt[e]=t/n}Vt[7]=vo/(8*17);Yt[7]=vo*8/17;R.fastSlerp=function(e,t,n,o){s.typeOf.object("start",e),s.typeOf.object("end",t),s.typeOf.number("t",n),s.typeOf.object("result",o);let i=R.dot(e,t),r;i>=0?r=1:(r=-1,i=-i);let a=i-1,u=1-n,d=n*n,m=u*u;for(let v=7;v>=0;--v)Ue[v]=(Vt[v]*d-Yt[v])*a,Ie[v]=(Vt[v]*m-Yt[v])*a;let l=r*n*(1+Ue[0]*(1+Ue[1]*(1+Ue[2]*(1+Ue[3]*(1+Ue[4]*(1+Ue[5]*(1+Ue[6]*(1+Ue[7])))))))),w=u*(1+Ie[0]*(1+Ie[1]*(1+Ie[2]*(1+Ie[3]*(1+Ie[4]*(1+Ie[5]*(1+Ie[6]*(1+Ie[7])))))))),T=R.multiplyByScalar(e,w,Yr);return R.multiplyByScalar(t,l,o),R.add(T,o,o)};R.fastSquad=function(e,t,n,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",t),s.typeOf.object("s0",n),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=R.fastSlerp(e,t,i,gt),u=R.fastSlerp(n,o,i,ct);return R.fastSlerp(a,u,2*i*(1-i),r)};R.equals=function(e,t){return e===t||p(e)&&p(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w};R.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||p(e)&&p(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n};R.ZERO=Object.freeze(new R(0,0,0,0));R.IDENTITY=Object.freeze(new R(0,0,0,1));R.prototype.clone=function(e){return R.clone(this,e)};R.prototype.equals=function(e){return R.equals(this,e)};R.prototype.equalsEpsilon=function(e,t){return R.equalsEpsilon(this,e,t)};R.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xe=R;var V={},wn={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},st={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},bn={},ge={east:new _,north:new _,up:new _,west:new _,south:new _,down:new _},Fe=new _,xe=new _,Le=new _;V.localFrameToFixedFrameGenerator=function(e,t){if(!wn.hasOwnProperty(e)||!wn[e].hasOwnProperty(t))throw new A("firstAxis and secondAxis must be east, north, up, west, south or down.");let n=wn[e][t],o,i=e+t;return p(bn[i])?o=bn[i]:(o=function(r,a,u){if(!p(r))throw new A("origin is required.");if(isNaN(r.x)||isNaN(r.y)||isNaN(r.z))throw new A("origin has a NaN component");if(p(u)||(u=new G),_.equalsEpsilon(r,_.ZERO,E.EPSILON14))_.unpack(st[e],0,Fe),_.unpack(st[t],0,xe),_.unpack(st[n],0,Le);else if(E.equalsEpsilon(r.x,0,E.EPSILON14)&&E.equalsEpsilon(r.y,0,E.EPSILON14)){let d=E.sign(r.z);_.unpack(st[e],0,Fe),e!=="east"&&e!=="west"&&_.multiplyByScalar(Fe,d,Fe),_.unpack(st[t],0,xe),t!=="east"&&t!=="west"&&_.multiplyByScalar(xe,d,xe),_.unpack(st[n],0,Le),n!=="east"&&n!=="west"&&_.multiplyByScalar(Le,d,Le)}else{a=O(a,ze.default),a.geodeticSurfaceNormal(r,ge.up);let d=ge.up,m=ge.east;m.x=-r.y,m.y=r.x,m.z=0,_.normalize(m,ge.east),_.cross(d,m,ge.north),_.multiplyByScalar(ge.up,-1,ge.down),_.multiplyByScalar(ge.east,-1,ge.west),_.multiplyByScalar(ge.north,-1,ge.south),Fe=ge[e],xe=ge[t],Le=ge[n]}return u[0]=Fe.x,u[1]=Fe.y,u[2]=Fe.z,u[3]=0,u[4]=xe.x,u[5]=xe.y,u[6]=xe.z,u[7]=0,u[8]=Le.x,u[9]=Le.y,u[10]=Le.z,u[11]=0,u[12]=r.x,u[13]=r.y,u[14]=r.z,u[15]=1,u},bn[i]=o),o};V.eastNorthUpToFixedFrame=V.localFrameToFixedFrameGenerator("east","north");V.northEastDownToFixedFrame=V.localFrameToFixedFrameGenerator("north","east");V.northUpEastToFixedFrame=V.localFrameToFixedFrameGenerator("north","up");V.northWestUpToFixedFrame=V.localFrameToFixedFrameGenerator("north","west");var Xr=new Xe,Zr=new _(1,1,1),Jr=new G;V.headingPitchRollToFixedFrame=function(e,t,n,o,i){s.typeOf.object("HeadingPitchRoll",t),o=O(o,V.eastNorthUpToFixedFrame);let r=Xe.fromHeadingPitchRoll(t,Xr),a=G.fromTranslationQuaternionRotationScale(_.ZERO,r,Zr,Jr);return i=o(e,n,i),G.multiply(i,a,i)};var Gr=new G,Kr=new W;V.headingPitchRollQuaternion=function(e,t,n,o,i){s.typeOf.object("HeadingPitchRoll",t);let r=V.headingPitchRollToFixedFrame(e,t,n,o,Gr),a=G.getMatrix3(r,Kr);return Xe.fromRotationMatrix(a,i)};var ei=new _(1,1,1),ti=new _,Co=new G,ni=new G,oi=new W,ri=new Xe;V.fixedFrameToHeadingPitchRoll=function(e,t,n,o){s.defined("transform",e),t=O(t,ze.default),n=O(n,V.eastNorthUpToFixedFrame),p(o)||(o=new Lt);let i=G.getTranslation(e,ti);if(_.equals(i,_.ZERO))return o.heading=0,o.pitch=0,o.roll=0,o;let r=G.inverseTransformation(n(i,t,Co),Co),a=G.setScale(e,ei,ni);a=G.setTranslation(a,_.ZERO,a),r=G.multiply(r,a,r);let u=Xe.fromRotationMatrix(G.getMatrix3(r,oi),ri);return u=Xe.normalize(u,u),Lt.fromQuaternion(u,o)};var ii=6*3600+41*60+50.54841,ci=8640184812866e-6,si=.093104,ai=-62e-7,fi=11772758384668e-32,ui=72921158553e-15,pi=E.TWO_PI/86400,Xt=new pe;V.computeIcrfToCentralBodyFixedMatrix=function(e,t){let n=V.computeIcrfToFixedMatrix(e,t);return p(n)||(n=V.computeTemeToPseudoFixedMatrix(e,t)),n};V.computeTemeToPseudoFixedMatrix=function(e,t){if(!p(e))throw new A("date is required.");Xt=pe.addSeconds(e,-pe.computeTaiMinusUtc(e),Xt);let n=Xt.dayNumber,o=Xt.secondsOfDay,i,r=n-2451545;o>=43200?i=(r+.5)/ce.DAYS_PER_JULIAN_CENTURY:i=(r-.5)/ce.DAYS_PER_JULIAN_CENTURY;let u=(ii+i*(ci+i*(si+i*ai)))*pi%E.TWO_PI,d=ui+fi*(n-24515455e-1),m=(o+ce.SECONDS_PER_DAY*.5)%ce.SECONDS_PER_DAY,l=u+d*m,w=Math.cos(l),T=Math.sin(l);return p(t)?(t[0]=w,t[1]=-T,t[2]=0,t[3]=T,t[4]=w,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new W(w,T,0,-T,w,0,0,0,1)};V.iau2006XysData=new go;V.earthOrientationParameters=mo.NONE;var _n=32.184,hi=2451545;V.preloadIcrfFixed=function(e){let t=e.start.dayNumber,n=e.start.secondsOfDay+_n,o=e.stop.dayNumber,i=e.stop.secondsOfDay+_n;return V.iau2006XysData.preload(t,n,o,i)};V.computeIcrfToFixedMatrix=function(e,t){if(!p(e))throw new A("date is required.");p(t)||(t=new W);let n=V.computeFixedToIcrfMatrix(e,t);if(p(n))return W.transpose(n,t)};var di=32.184,mi=2451545,Zt=new Lt,yi=new W,li=new pe;V.computeMoonFixedToIcrfMatrix=function(e,t){if(!p(e))throw new A("date is required.");p(t)||(t=new W);let n=pe.addSeconds(e,di,li),o=pe.totalDays(n)-mi,i=E.toRadians(12.112)-E.toRadians(.052992)*o,r=E.toRadians(24.224)-E.toRadians(.105984)*o,a=E.toRadians(227.645)+E.toRadians(13.012)*o,u=E.toRadians(261.105)+E.toRadians(13.340716)*o,d=E.toRadians(358)+E.toRadians(.9856)*o;return Zt.pitch=E.toRadians(180)-E.toRadians(3.878)*Math.sin(i)-E.toRadians(.12)*Math.sin(r)+E.toRadians(.07)*Math.sin(a)-E.toRadians(.017)*Math.sin(u),Zt.roll=E.toRadians(66.53-90)+E.toRadians(1.543)*Math.cos(i)+E.toRadians(.24)*Math.cos(r)-E.toRadians(.028)*Math.cos(a)+E.toRadians(.007)*Math.cos(u),Zt.heading=E.toRadians(244.375-90)+E.toRadians(13.17635831)*o+E.toRadians(3.558)*Math.sin(i)+E.toRadians(.121)*Math.sin(r)-E.toRadians(.064)*Math.sin(a)+E.toRadians(.016)*Math.sin(u)+E.toRadians(.025)*Math.sin(d),W.fromHeadingPitchRoll(Zt,yi)};V.computeIcrfToMoonFixedMatrix=function(e,t){if(!p(e))throw new A("date is required.");p(t)||(t=new W);let n=V.computeMoonFixedToIcrfMatrix(e,t);if(p(n))return W.transpose(n,t)};var wi=new Qt(0,0,0),bi=new pt(0,0,0,0,0,0),On=new W,gn=new W;V.computeFixedToIcrfMatrix=function(e,t){if(!p(e))throw new A("date is required.");p(t)||(t=new W);let n=V.earthOrientationParameters.compute(e,bi);if(!p(n))return;let o=e.dayNumber,i=e.secondsOfDay+_n,r=V.iau2006XysData.computeXysRadians(o,i,wi);if(!p(r))return;let a=r.x+n.xPoleOffset,u=r.y+n.yPoleOffset,d=1/(1+Math.sqrt(1-a*a-u*u)),m=On;m[0]=1-d*a*a,m[3]=-d*a*u,m[6]=a,m[1]=-d*a*u,m[4]=1-d*u*u,m[7]=u,m[2]=-a,m[5]=-u,m[8]=1-d*(a*a+u*u);let l=W.fromRotationZ(-r.s,gn),w=W.multiply(m,l,On),T=e.dayNumber,v=e.secondsOfDay-pe.computeTaiMinusUtc(e)+n.ut1MinusUtc,P=T-2451545,j=v/ce.SECONDS_PER_DAY,q=.779057273264+j+.00273781191135448*(P+j);q=q%1*E.TWO_PI;let k=W.fromRotationZ(q,gn),x=W.multiply(w,k,On),F=Math.cos(n.xPoleWander),B=Math.cos(n.yPoleWander),Q=Math.sin(n.xPoleWander),H=Math.sin(n.yPoleWander),ee=o-hi+i/ce.SECONDS_PER_DAY;ee/=36525;let re=-47e-6*ee*E.RADIANS_PER_DEGREE/3600,Z=Math.cos(re),oe=Math.sin(re),J=gn;return J[0]=F*Z,J[1]=F*oe,J[2]=Q,J[3]=-B*oe+H*Q*Z,J[4]=B*Z+H*Q*oe,J[5]=-H*F,J[6]=-H*oe-B*Q*Z,J[7]=H*Z-B*Q*oe,J[8]=B*F,W.multiply(x,J,t)};var Oi=new qe;V.pointToWindowCoordinates=function(e,t,n,o){return o=V.pointToGLWindowCoordinates(e,t,n,o),o.y=2*t[5]-o.y,o};V.pointToGLWindowCoordinates=function(e,t,n,o){if(!p(e))throw new A("modelViewProjectionMatrix is required.");if(!p(t))throw new A("viewportTransformation is required.");if(!p(n))throw new A("point is required.");p(o)||(o=new Oe);let i=Oi;return G.multiplyByVector(e,qe.fromElements(n.x,n.y,n.z,1,i),i),qe.multiplyByScalar(i,1/i.w,i),G.multiplyByVector(t,i,i),Oe.fromCartesian4(i,o)};var gi=new _,_i=new _,Si=new _;V.rotationMatrixFromPositionVelocity=function(e,t,n,o){if(!p(e))throw new A("position is required.");if(!p(t))throw new A("velocity is required.");let i=O(n,ze.default).geodeticSurfaceNormal(e,gi),r=_.cross(t,i,_i);_.equalsEpsilon(r,_.ZERO,E.EPSILON6)&&(r=_.clone(_.UNIT_X,r));let a=_.cross(r,t,Si);return _.normalize(a,a),_.cross(t,a,r),_.negate(r,r),_.normalize(r,r),p(o)||(o=new W),o[0]=t.x,o[1]=t.y,o[2]=t.z,o[3]=r.x,o[4]=r.y,o[5]=r.z,o[6]=a.x,o[7]=a.y,o[8]=a.z,o};var Ao=new G(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),jo=new Me,Sn=new _,Ri=new _,Ei=new W,Rn=new G,Mo=new G;V.basisTo2D=function(e,t,n){if(!p(e))throw new A("projection is required.");if(!p(t))throw new A("matrix is required.");if(!p(n))throw new A("result is required.");let o=G.getTranslation(t,Ri),i=e.ellipsoid,r;if(_.equals(o,_.ZERO))r=_.clone(_.ZERO,Sn);else{let l=i.cartesianToCartographic(o,jo);r=e.project(l,Sn),_.fromElements(r.z,r.x,r.y,r)}let a=V.eastNorthUpToFixedFrame(o,i,Rn),u=G.inverseTransformation(a,Mo),d=G.getMatrix3(t,Ei),m=G.multiplyByMatrix3(u,d,n);return G.multiply(Ao,m,n),G.setTranslation(n,r,n),n};V.ellipsoidTo2DModelMatrix=function(e,t,n){if(!p(e))throw new A("projection is required.");if(!p(t))throw new A("center is required.");if(!p(n))throw new A("result is required.");let o=e.ellipsoid,i=V.eastNorthUpToFixedFrame(t,o,Rn),r=G.inverseTransformation(i,Mo),a=o.cartesianToCartographic(t,jo),u=e.project(a,Sn);_.fromElements(u.z,u.x,u.y,u);let d=G.fromTranslation(u,Rn);return G.multiply(Ao,r,n),G.multiply(d,n,n),n};var Po=V;var Uo={};function _t(e,t){if(!p(e))throw new A("identifier is required.");p(Uo[e])||(Uo[e]=!0,console.warn(O(t,e)))}_t.geometryOutlines="Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.";_t.geometryZIndex="Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored";_t.geometryHeightReference="Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored";_t.geometryExtrudedHeightReference="Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored";var Io=_t;function Ti(e,t){if(!p(e)||!p(t))throw new A("identifier and message are required.");Io(e,t)}var zo=Ti;function L(e,t,n,o){this.west=O(e,0),this.south=O(t,0),this.east=O(n,0),this.north=O(o,0)}Object.defineProperties(L.prototype,{width:{get:function(){return L.computeWidth(this)}},height:{get:function(){return L.computeHeight(this)}}});L.packedLength=4;L.pack=function(e,t,n){return s.typeOf.object("value",e),s.defined("array",t),n=O(n,0),t[n++]=e.west,t[n++]=e.south,t[n++]=e.east,t[n]=e.north,t};L.unpack=function(e,t,n){return s.defined("array",e),t=O(t,0),p(n)||(n=new L),n.west=e[t++],n.south=e[t++],n.east=e[t++],n.north=e[t],n};L.computeWidth=function(e){s.typeOf.object("rectangle",e);let t=e.east,n=e.west;return t<n&&(t+=E.TWO_PI),t-n};L.computeHeight=function(e){return s.typeOf.object("rectangle",e),e.north-e.south};L.fromDegrees=function(e,t,n,o,i){return e=E.toRadians(O(e,0)),t=E.toRadians(O(t,0)),n=E.toRadians(O(n,0)),o=E.toRadians(O(o,0)),p(i)?(i.west=e,i.south=t,i.east=n,i.north=o,i):new L(e,t,n,o)};L.fromRadians=function(e,t,n,o,i){return p(i)?(i.west=O(e,0),i.south=O(t,0),i.east=O(n,0),i.north=O(o,0),i):new L(e,t,n,o)};L.fromCartographicArray=function(e,t){s.defined("cartographics",e);let n=Number.MAX_VALUE,o=-Number.MAX_VALUE,i=Number.MAX_VALUE,r=-Number.MAX_VALUE,a=Number.MAX_VALUE,u=-Number.MAX_VALUE;for(let d=0,m=e.length;d<m;d++){let l=e[d];n=Math.min(n,l.longitude),o=Math.max(o,l.longitude),a=Math.min(a,l.latitude),u=Math.max(u,l.latitude);let w=l.longitude>=0?l.longitude:l.longitude+E.TWO_PI;i=Math.min(i,w),r=Math.max(r,w)}return o-n>r-i&&(n=i,o=r,o>E.PI&&(o=o-E.TWO_PI),n>E.PI&&(n=n-E.TWO_PI)),p(t)?(t.west=n,t.south=a,t.east=o,t.north=u,t):new L(n,a,o,u)};L.fromCartesianArray=function(e,t,n){s.defined("cartesians",e),t=O(t,ze.default);let o=Number.MAX_VALUE,i=-Number.MAX_VALUE,r=Number.MAX_VALUE,a=-Number.MAX_VALUE,u=Number.MAX_VALUE,d=-Number.MAX_VALUE;for(let m=0,l=e.length;m<l;m++){let w=t.cartesianToCartographic(e[m]);o=Math.min(o,w.longitude),i=Math.max(i,w.longitude),u=Math.min(u,w.latitude),d=Math.max(d,w.latitude);let T=w.longitude>=0?w.longitude:w.longitude+E.TWO_PI;r=Math.min(r,T),a=Math.max(a,T)}return i-o>a-r&&(o=r,i=a,i>E.PI&&(i=i-E.TWO_PI),o>E.PI&&(o=o-E.TWO_PI)),p(n)?(n.west=o,n.south=u,n.east=i,n.north=d,n):new L(o,u,i,d)};var vi=new _,Ci=new _,Ai=new _,ji=new _,Mi=new _,En=new Array(5);for(let e=0;e<En.length;++e)En[e]=new _;L.fromBoundingSphere=function(e,t,n){s.typeOf.object("boundingSphere",e);let o=e.center,i=e.radius;if(p(t)||(t=ze.default),p(n)||(n=new L),_.equals(o,_.ZERO))return L.clone(L.MAX_VALUE,n),n;let r=Po.eastNorthUpToFixedFrame(o,t,vi),a=G.multiplyByPointAsVector(r,_.UNIT_X,Ci);_.normalize(a,a);let u=G.multiplyByPointAsVector(r,_.UNIT_Y,Ai);_.normalize(u,u),_.multiplyByScalar(u,i,u),_.multiplyByScalar(a,i,a);let d=_.negate(u,Mi),m=_.negate(a,ji),l=En,w=l[0];return _.add(o,u,w),w=l[1],_.add(o,m,w),w=l[2],_.add(o,d,w),w=l[3],_.add(o,a,w),l[4]=o,L.fromCartesianArray(l,t,n)};L.clone=function(e,t){if(p(e))return p(t)?(t.west=e.west,t.south=e.south,t.east=e.east,t.north=e.north,t):new L(e.west,e.south,e.east,e.north)};L.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||p(e)&&p(t)&&Math.abs(e.west-t.west)<=n&&Math.abs(e.south-t.south)<=n&&Math.abs(e.east-t.east)<=n&&Math.abs(e.north-t.north)<=n};L.prototype.clone=function(e){return L.clone(this,e)};L.prototype.equals=function(e){return L.equals(this,e)};L.equals=function(e,t){return e===t||p(e)&&p(t)&&e.west===t.west&&e.south===t.south&&e.east===t.east&&e.north===t.north};L.prototype.equalsEpsilon=function(e,t){return L.equalsEpsilon(this,e,t)};L.validate=function(e){return zo("Rectangle.validate","Rectangle.validate is a no-op and has been deprecated. It will be removed in Cesium 1.124."),L._validate(e)};L._validate=function(e){s.typeOf.object("rectangle",e);let t=e.north;s.typeOf.number.greaterThanOrEquals("north",t,-E.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("north",t,E.PI_OVER_TWO);let n=e.south;s.typeOf.number.greaterThanOrEquals("south",n,-E.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("south",n,E.PI_OVER_TWO);let o=e.west;s.typeOf.number.greaterThanOrEquals("west",o,-Math.PI),s.typeOf.number.lessThanOrEquals("west",o,Math.PI);let i=e.east;s.typeOf.number.greaterThanOrEquals("east",i,-Math.PI),s.typeOf.number.lessThanOrEquals("east",i,Math.PI)};L.southwest=function(e,t){return s.typeOf.object("rectangle",e),p(t)?(t.longitude=e.west,t.latitude=e.south,t.height=0,t):new Me(e.west,e.south)};L.northwest=function(e,t){return s.typeOf.object("rectangle",e),p(t)?(t.longitude=e.west,t.latitude=e.north,t.height=0,t):new Me(e.west,e.north)};L.northeast=function(e,t){return s.typeOf.object("rectangle",e),p(t)?(t.longitude=e.east,t.latitude=e.north,t.height=0,t):new Me(e.east,e.north)};L.southeast=function(e,t){return s.typeOf.object("rectangle",e),p(t)?(t.longitude=e.east,t.latitude=e.south,t.height=0,t):new Me(e.east,e.south)};L.center=function(e,t){s.typeOf.object("rectangle",e);let n=e.east,o=e.west;n<o&&(n+=E.TWO_PI);let i=E.negativePiToPi((o+n)*.5),r=(e.south+e.north)*.5;return p(t)?(t.longitude=i,t.latitude=r,t.height=0,t):new Me(i,r)};L.intersection=function(e,t,n){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",t);let o=e.east,i=e.west,r=t.east,a=t.west;o<i&&r>0?o+=E.TWO_PI:r<a&&o>0&&(r+=E.TWO_PI),o<i&&a<0?a+=E.TWO_PI:r<a&&i<0&&(i+=E.TWO_PI);let u=E.negativePiToPi(Math.max(i,a)),d=E.negativePiToPi(Math.min(o,r));if((e.west<e.east||t.west<t.east)&&d<=u)return;let m=Math.max(e.south,t.south),l=Math.min(e.north,t.north);if(!(m>=l))return p(n)?(n.west=u,n.south=m,n.east=d,n.north=l,n):new L(u,m,d,l)};L.simpleIntersection=function(e,t,n){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",t);let o=Math.max(e.west,t.west),i=Math.max(e.south,t.south),r=Math.min(e.east,t.east),a=Math.min(e.north,t.north);if(!(i>=a||o>=r))return p(n)?(n.west=o,n.south=i,n.east=r,n.north=a,n):new L(o,i,r,a)};L.union=function(e,t,n){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",t),p(n)||(n=new L);let o=e.east,i=e.west,r=t.east,a=t.west;o<i&&r>0?o+=E.TWO_PI:r<a&&o>0&&(r+=E.TWO_PI),o<i&&a<0?a+=E.TWO_PI:r<a&&i<0&&(i+=E.TWO_PI);let u=E.negativePiToPi(Math.min(i,a)),d=E.negativePiToPi(Math.max(o,r));return n.west=u,n.south=Math.min(e.south,t.south),n.east=d,n.north=Math.max(e.north,t.north),n};L.expand=function(e,t,n){return s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",t),p(n)||(n=new L),n.west=Math.min(e.west,t.longitude),n.south=Math.min(e.south,t.latitude),n.east=Math.max(e.east,t.longitude),n.north=Math.max(e.north,t.latitude),n};L.contains=function(e,t){s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",t);let n=t.longitude,o=t.latitude,i=e.west,r=e.east;return r<i&&(r+=E.TWO_PI,n<0&&(n+=E.TWO_PI)),(n>i||E.equalsEpsilon(n,i,E.EPSILON14))&&(n<r||E.equalsEpsilon(n,r,E.EPSILON14))&&o>=e.south&&o<=e.north};var Pi=new Me;L.subsample=function(e,t,n,o){s.typeOf.object("rectangle",e),t=O(t,ze.default),n=O(n,0),p(o)||(o=[]);let i=0,r=e.north,a=e.south,u=e.east,d=e.west,m=Pi;m.height=n,m.longitude=d,m.latitude=r,o[i]=t.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=t.cartographicToCartesian(m,o[i]),i++,m.latitude=a,o[i]=t.cartographicToCartesian(m,o[i]),i++,m.longitude=d,o[i]=t.cartographicToCartesian(m,o[i]),i++,r<0?m.latitude=r:a>0?m.latitude=a:m.latitude=0;for(let l=1;l<8;++l)m.longitude=-Math.PI+l*E.PI_OVER_TWO,L.contains(e,m)&&(o[i]=t.cartographicToCartesian(m,o[i]),i++);return m.latitude===0&&(m.longitude=d,o[i]=t.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=t.cartographicToCartesian(m,o[i]),i++),o.length=i,o};L.subsection=function(e,t,n,o,i,r){if(s.typeOf.object("rectangle",e),s.typeOf.number.greaterThanOrEquals("westLerp",t,0),s.typeOf.number.lessThanOrEquals("westLerp",t,1),s.typeOf.number.greaterThanOrEquals("southLerp",n,0),s.typeOf.number.lessThanOrEquals("southLerp",n,1),s.typeOf.number.greaterThanOrEquals("eastLerp",o,0),s.typeOf.number.lessThanOrEquals("eastLerp",o,1),s.typeOf.number.greaterThanOrEquals("northLerp",i,0),s.typeOf.number.lessThanOrEquals("northLerp",i,1),s.typeOf.number.lessThanOrEquals("westLerp",t,o),s.typeOf.number.lessThanOrEquals("southLerp",n,i),p(r)||(r=new L),e.west<=e.east){let u=e.east-e.west;r.west=e.west+t*u,r.east=e.west+o*u}else{let u=E.TWO_PI+e.east-e.west;r.west=E.negativePiToPi(e.west+t*u),r.east=E.negativePiToPi(e.west+o*u)}let a=e.north-e.south;return r.south=e.south+n*a,r.north=e.south+i*a,t===1&&(r.west=e.east),o===1&&(r.east=e.east),n===1&&(r.south=e.north),i===1&&(r.north=e.north),r};L.MAX_VALUE=Object.freeze(new L(-Math.PI,-E.PI_OVER_TWO,Math.PI,E.PI_OVER_TWO));var Af=L;function D(e,t,n,o){this[0]=O(e,0),this[1]=O(n,0),this[2]=O(t,0),this[3]=O(o,0)}D.packedLength=4;D.pack=function(e,t,n){return s.typeOf.object("value",e),s.defined("array",t),n=O(n,0),t[n++]=e[0],t[n++]=e[1],t[n++]=e[2],t[n++]=e[3],t};D.unpack=function(e,t,n){return s.defined("array",e),t=O(t,0),p(n)||(n=new D),n[0]=e[t++],n[1]=e[t++],n[2]=e[t++],n[3]=e[t++],n};D.packArray=function(e,t){s.defined("array",e);let n=e.length,o=n*4;if(!p(t))t=new Array(o);else{if(!Array.isArray(t)&&t.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");t.length!==o&&(t.length=o)}for(let i=0;i<n;++i)D.pack(e[i],t,i*4);return t};D.unpackArray=function(e,t){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let n=e.length;p(t)?t.length=n/4:t=new Array(n/4);for(let o=0;o<n;o+=4){let i=o/4;t[i]=D.unpack(e,o,t[i])}return t};D.clone=function(e,t){if(p(e))return p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new D(e[0],e[2],e[1],e[3])};D.fromArray=D.unpack;D.fromColumnMajorArray=function(e,t){return s.defined("values",e),D.clone(e,t)};D.fromRowMajorArray=function(e,t){return s.defined("values",e),p(t)?(t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3],t):new D(e[0],e[1],e[2],e[3])};D.fromScale=function(e,t){return s.typeOf.object("scale",e),p(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=e.y,t):new D(e.x,0,0,e.y)};D.fromUniformScale=function(e,t){return s.typeOf.number("scale",e),p(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=e,t):new D(e,0,0,e)};D.fromRotation=function(e,t){s.typeOf.number("angle",e);let n=Math.cos(e),o=Math.sin(e);return p(t)?(t[0]=n,t[1]=o,t[2]=-o,t[3]=n,t):new D(n,-o,o,n)};D.toArray=function(e,t){return s.typeOf.object("matrix",e),p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):[e[0],e[1],e[2],e[3]]};D.getElementIndex=function(e,t){return s.typeOf.number.greaterThanOrEquals("row",t,0),s.typeOf.number.lessThanOrEquals("row",t,1),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,1),e*2+t};D.getColumn=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,1),s.typeOf.object("result",n);let o=t*2,i=e[o],r=e[o+1];return n.x=i,n.y=r,n};D.setColumn=function(e,t,n,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,1),s.typeOf.object("cartesian",n),s.typeOf.object("result",o),o=D.clone(e,o);let i=t*2;return o[i]=n.x,o[i+1]=n.y,o};D.getRow=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,1),s.typeOf.object("result",n);let o=e[t],i=e[t+2];return n.x=o,n.y=i,n};D.setRow=function(e,t,n,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",t,0),s.typeOf.number.lessThanOrEquals("index",t,1),s.typeOf.object("cartesian",n),s.typeOf.object("result",o),o=D.clone(e,o),o[t]=n.x,o[t+2]=n.y,o};var Ui=new Oe;D.setScale=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("scale",t),s.typeOf.object("result",n);let o=D.getScale(e,Ui),i=t.x/o.x,r=t.y/o.y;return n[0]=e[0]*i,n[1]=e[1]*i,n[2]=e[2]*r,n[3]=e[3]*r,n};var Ii=new Oe;D.setUniformScale=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.number("scale",t),s.typeOf.object("result",n);let o=D.getScale(e,Ii),i=t/o.x,r=t/o.y;return n[0]=e[0]*i,n[1]=e[1]*i,n[2]=e[2]*r,n[3]=e[3]*r,n};var qo=new Oe;D.getScale=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t.x=Oe.magnitude(Oe.fromElements(e[0],e[1],qo)),t.y=Oe.magnitude(Oe.fromElements(e[2],e[3],qo)),t};var Do=new Oe;D.getMaximumScale=function(e){return D.getScale(e,Do),Oe.maximumComponent(Do)};var zi=new Oe;D.setRotation=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let o=D.getScale(e,zi);return n[0]=t[0]*o.x,n[1]=t[1]*o.x,n[2]=t[2]*o.y,n[3]=t[3]*o.y,n};var qi=new Oe;D.getRotation=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=D.getScale(e,qi);return t[0]=e[0]/n.x,t[1]=e[1]/n.x,t[2]=e[2]/n.y,t[3]=e[3]/n.y,t};D.multiply=function(e,t,n){s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n);let o=e[0]*t[0]+e[2]*t[1],i=e[0]*t[2]+e[2]*t[3],r=e[1]*t[0]+e[3]*t[1],a=e[1]*t[2]+e[3]*t[3];return n[0]=o,n[1]=r,n[2]=i,n[3]=a,n};D.add=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n};D.subtract=function(e,t,n){return s.typeOf.object("left",e),s.typeOf.object("right",t),s.typeOf.object("result",n),n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n};D.multiplyByVector=function(e,t,n){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",t),s.typeOf.object("result",n);let o=e[0]*t.x+e[2]*t.y,i=e[1]*t.x+e[3]*t.y;return n.x=o,n.y=i,n};D.multiplyByScalar=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",t),s.typeOf.object("result",n),n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n};D.multiplyByScale=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.object("scale",t),s.typeOf.object("result",n),n[0]=e[0]*t.x,n[1]=e[1]*t.x,n[2]=e[2]*t.y,n[3]=e[3]*t.y,n};D.multiplyByUniformScale=function(e,t,n){return s.typeOf.object("matrix",e),s.typeOf.number("scale",t),s.typeOf.object("result",n),n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n};D.negate=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t};D.transpose=function(e,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let n=e[0],o=e[2],i=e[1],r=e[3];return t[0]=n,t[1]=o,t[2]=i,t[3]=r,t};D.abs=function(e,t){return s.typeOf.object("matrix",e),s.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t};D.equals=function(e,t){return e===t||p(e)&&p(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]};D.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]};D.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||p(e)&&p(t)&&Math.abs(e[0]-t[0])<=n&&Math.abs(e[1]-t[1])<=n&&Math.abs(e[2]-t[2])<=n&&Math.abs(e[3]-t[3])<=n};D.IDENTITY=Object.freeze(new D(1,0,0,1));D.ZERO=Object.freeze(new D(0,0,0,0));D.COLUMN0ROW0=0;D.COLUMN0ROW1=1;D.COLUMN1ROW0=2;D.COLUMN1ROW1=3;Object.defineProperties(D.prototype,{length:{get:function(){return D.packedLength}}});D.prototype.clone=function(e){return D.clone(this,e)};D.prototype.equals=function(e){return D.equals(this,e)};D.prototype.equalsEpsilon=function(e,t){return D.equalsEpsilon(this,e,t)};D.prototype.toString=function(){return`(${this[0]}, ${this[2]})
(${this[1]}, ${this[3]})`};var qf=D;export{qe as a,G as b,De as c,ke as d,Oo as e,Xe as f,Po as g,Io as h,Af as i,qf as j};
