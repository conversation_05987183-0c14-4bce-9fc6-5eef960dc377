<template>
  <div id="map" v-loading="loading">
    <div class="elevation-info">
      <p v-if="elevation">高程数据: {{ elevation }}</p>
      <p v-else-if="error">{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import "ol/ol.css";
import TileLayer from "ol/layer/Tile";
import XYZ from "ol/source/XYZ";
import { Map, View } from "ol";
import { ElMessage } from "element-plus";
import axios from "axios";

const map = ref(null);
const elevation = ref(null);
const error = ref(null);
const loading = ref(false);

const fetchElevation = async (lon, lat) => {
  loading.value = true;
  try {
    const response = await axios.get(`http://localhost:3001/api/getElevation?lon=${lon}&lat=${lat}`);
    elevation.value = response.data.elevation;
    console.log("获取高程成功:", elevation.value);
    error.value = null;
    loading.value = false;
  } catch (error) {
    if (error.response) {
      error.value = error.response.data.error;
    } else {
      error.value = "请求出错，请稍后重试";
    }
    elevation.value = null;
  }
};

const initMap = () => {
  map.value = new Map({
    target: "map",
    layers: [
      new TileLayer({
        source: new XYZ({
          url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
          attribution: "Esri World Imagery",
        }),
      }),
    ],
    view: new View({
      projection: "EPSG:4326",
      center: [118.481, 32.01],
      zoom: 13,
      maxZoom: 14,
    }),
  });

  // 添加地图点击事件
  map.value.on("click", (event) => {
    console.log("点击坐标:", event.coordinate);
    const [lon, lat] = event.coordinate;
    fetchElevation(lon, lat);
  });
};

onMounted(async () => {
  try {
    initMap();
  } catch (error) {
    ElMessage.error("初始化地图失败");
    console.error("初始化错误:", error);
  }
});

onUnmounted(() => {
  if (map.value) {
    map.value.setTarget(null);
    map.value = null;
  }
});
</script>

<style scoped>
#map {
  height: 100%;
  width: 100%;
}

.elevation-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.8);
  width: 100px;
  padding: 10px;
  border-radius: 4px;
  z-index: 1000;
}
</style>
