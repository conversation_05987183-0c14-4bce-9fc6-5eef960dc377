<script setup>
import { ref, onMounted } from "vue";

const JSMpeg = window.JSMpeg;

// 1. RTSP 源地址
const rtspUrl = "rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4";

// 2. 将 RTSP 地址用 btoa 编码，并拼成后端 WS 接口地址
const wsUrl = `ws://localhost:9999/rtsp?url=${btoa(rtspUrl)}`;

// 3. canvas 元素的 ref
const canvasRef = ref(null);

onMounted(() => {
  if (!canvasRef.value) {
    console.error("canvas 元素未就绪");
    return;
  }

  // 4. 用 JSMpeg 播放
  new JSMpeg.Player(wsUrl, {
    canvas: canvasRef.value,
    autoplay: true,
    audio: true, // 如果需要音频可以设为 true
  });
});
</script>

<template>
  <canvas ref="canvasRef" style="width: 600px; height: 400px; background: #000"></canvas>
</template>

<style scoped lang="scss">
/* 这里不写任何 JS 逻辑，只留样式即可 */
</style>
