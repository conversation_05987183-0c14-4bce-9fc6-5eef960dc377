<template>
  <div>
    <div id="container"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";
import { fetchGraphData } from "@/api";

const mockData = ref(null);

const getData = async () => {
  const res = await fetchGraphData();
  mockData.value = res.data;
  console.log(treeToGraphData(res.data));
};

onMounted(async () => {
  await getData();
  const graph = new Graph({
    container: document.getElementById("container"),
    autoFit: "view",
    data: treeToGraphData(mockData.value),
    behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
    node: {
      style: {
        labelText: (d) => d.id,
        labelBackground: true,
      },
      animation: {
        enter: false,
      },
    },
    layout: {
      type: "dendrogram",
      radial: true,
      nodeSep: 40,
      rankSep: 140,
      gpuEnabled: true,
    },
  });
  graph.render();
});
</script>
