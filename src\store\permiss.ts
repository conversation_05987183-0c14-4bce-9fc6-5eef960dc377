import { defineStore } from "pinia";

interface ObjectList {
  [key: string]: string[];
}

export const usePermissStore = defineStore("permiss", {
  state: () => {
    const defaultList: ObjectList = {
      admin: [
        "0",
        "1",
        "11",
        "12",
        "13",
        "2",
        "21",
        "22",
        "23",
        "24",
        "25",
        "26",
        "27",
        "28",
        "29",
        "291",
        "292",
        "3",
        "31",
        "32",
        "33",
        "34",
        "4",
        "41",
        "42",
        "5",
        "7",
        "6",
        "61",
        "62",
        "63",
        "64",
        "65",
        "66",
        "99",
        "88",
        "87",
        "86",
        "85",
        "84",
        "83",
        "82",
        "81",
      ],
      user: ["0", "1", "11", "12", "13", "99", "88", "87", "86", "85", "84", "83", "82", "81"],
    };
    const username = localStorage.getItem("vuems_name");
    console.log(username);
    return {
      key: (username == "admin" ? defaultList.admin : defaultList.user) as string[],
      defaultList,
    };
  },
  actions: {
    handleSet(val: string[]) {
      this.key = val;
    },
  },
});
