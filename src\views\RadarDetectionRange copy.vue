<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import {
  EllipsoidTerrainProvider,
  UrlTemplateImageryProvider,
  Cartesian3,
  Color,
  Rectangle,
  Transforms,
  Matrix4,
} from "cesium";
import { radarDetectionArea } from "@/utils/radarPower.js";

// —— Cesium 初始化 ——
const mapContainer = ref(null);
let viewer;
function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webrd0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}",
      subdomains: ["1", "2", "3", "4"],
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees((124.8886 + 131.1114) / 2, (33.482 + 38.518) / 2, 500000),
    orientation: { heading: 0, pitch: -Math.PI / 2, roll: 0 },
  });
  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );
}

function destinationVincenty(lat, lon, bearing, distance) {
  const a = 6378137,
    b = 6356752.3142,
    f = 1 / 298.257223563;
  const s = distance; // 米
  const alpha1 = Cesium.Math.toRadians(bearing);
  const sinAlpha1 = Math.sin(alpha1),
    cosAlpha1 = Math.cos(alpha1);

  const tanU1 = (1 - f) * Math.tan(Cesium.Math.toRadians(lat));
  const cosU1 = 1 / Math.sqrt(1 + tanU1 * tanU1);
  const sinU1 = tanU1 * cosU1;

  const sigma1 = Math.atan2(tanU1, cosAlpha1);
  const sinAlpha = cosU1 * sinAlpha1;
  const cosSqAlpha = 1 - sinAlpha * sinAlpha;
  const uSq = (cosSqAlpha * (a * a - b * b)) / (b * b);
  const A = 1 + (uSq / 16384) * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
  const B = (uSq / 1024) * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));

  let sigma = s / (b * A),
    sigmaP = 2 * Math.PI;
  let sinSigma, cosSigma, cos2SigmaM, deltaSigma;
  while (Math.abs(sigma - sigmaP) > 1e-12) {
    cos2SigmaM = Math.cos(2 * sigma1 + sigma);
    sinSigma = Math.sin(sigma);
    cosSigma = Math.cos(sigma);
    deltaSigma =
      B *
      sinSigma *
      (cos2SigmaM +
        (B / 4) *
          (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            (B / 6) * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
    sigmaP = sigma;
    sigma = s / (b * A) + deltaSigma;
  }

  const tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
  const lat2 = Math.atan2(
    sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
    (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp)
  );
  const lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
  const C = (f / 16) * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
  const L =
    lambda -
    (1 - C) * f * sinAlpha * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));
  const lon2 = Cesium.Math.toRadians(lon) + L;

  return {
    lat: Cesium.Math.toDegrees(lat2),
    lon: Cesium.Math.toDegrees(lon2),
  };
}

function drawRadarCoverage({ radarLon, radarLat, R_max, nRow, nCol }) {
  viewer.entities.removeAll(); // 清空当前显示

  const radarCartesian = Cesium.Cartesian3.fromDegrees(radarLon, radarLat, 0); // 雷达位置
  const radarAzimuthStep = 360 / nCol; // 方位角间隔

  // 计算并绘制每个仰角的覆盖区域
  for (let i = 0; i < nRow; i++) {
    const elevationAngle = (i / (nRow - 1)) * 30; // 线性分布仰角从0到30度
    const points = [];

    // 计算每个方位角的点
    for (let j = 0; j < nCol; j++) {
      const azimuth = j * radarAzimuthStep;
      const range = R_max[i][j];
      const point = destinationVincenty(radarLat, radarLon, azimuth, range);
      points.push(Cesium.Cartesian3.fromDegrees(point.lon, point.lat)); // 转换为笛卡尔坐标
    }

    // 闭合图形
    points.push(points[0]);

    // 绘制该仰角层的探测范围（扇形）
    viewer.entities.add({
      name: `雷达探测范围 - 仰角 ${elevationAngle}°`,
      polygon: {
        hierarchy: points,
        material: Cesium.Color.YELLOW.withAlpha(0.3), // 填充颜色，调整透明度
        outline: true, // 是否绘制边界
        outlineColor: Cesium.Color.YELLOW,
      },
    });
  }
}

// —— 示范：假设你已经通过 fetch 拿到上面那个 JSON ——
onMounted(async () => {
  initMap();

  const powerData = radarDetectionArea;
  const { nRow, nCol, R_max } = powerData.data;

  // 请根据您的实际数据定义雷达的经纬度和高度
  const radarLon = 118.8; // 例如：南京经度
  const radarLat = 32.05; // 例如：南京纬度

  // 核心：定义每个仰角层对应的具体仰角值（度）
  // 这里的示例假设仰角从0度均匀分布到 maxElevationAngle
  // 您需要根据您的 R_max 数据实际代表的仰角值来调整这个数组

  drawRadarCoverage({
    radarLon,
    radarLat,
    R_max,
    nRow,
    nCol,
  });

  // 调整相机视角以便更好地查看三维雷达图
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(radarLon, radarLat, 20000), // 稍微抬高相机高度
    orientation: {
      heading: Cesium.Math.toRadians(45), // 从一个斜向角度看
      pitch: Cesium.Math.toRadians(-30), // 俯视角度
      roll: 0,
    },
    duration: 3, // 飞行时间
  });
});

onBeforeUnmount(() => {
  viewer && viewer.destroy();
});
</script>

<style scoped>
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
