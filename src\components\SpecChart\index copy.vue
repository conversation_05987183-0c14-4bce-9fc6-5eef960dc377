<!-- FrequencyChart.vue -->
<template>
  <div ref="chartRef" class="freq-chart" />
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
  import * as echarts from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components'
  import { CustomChart, BarChart } from 'echarts/charts'

  // 注册 ECharts 模块
  echarts.use([
    CanvasRenderer,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    CustomChart,
    BarChart
  ])

  const props = defineProps({
    categories: {
      type: Array,
      default: () => []
    },
    availableRanges: {
      type: Array,
      default: () => []
    },
    disabledRanges: {
      type: Array,
      default: () => []
    },
    conflictRanges: {
      type: Array,
      default: () => []
    },
    reservedRanges: {
      type: Array,
      default: () => []
    },
    protectedRanges: {
      type: Array,
      default: () => []
    },
    xAxisMin: {
      type: Number,
      default: 30
    },
    xAxisMax: {
      type: Number,
      default: 18000
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    legendData: {
      type: Array,
      default: () => ['可用频率', '禁用频率', '冲突频率', '预留频率', '保护频率']
    },
    showYAxisLabel: {
      type: Boolean,
      default: true
    }
  })

  // 图表容器和实例
  const chartRef = ref(null)
  let chartInstance = null

  // 组件卸载时销毁实例
  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    window.removeEventListener('resize', resizeChart)
  })

  // 构建 ECharts 配置函数 - 使用自定义系列实现
  function makeOption(categories, available, disabled, conflict, reserved, protect) {
    // 颜色映射 - 确保颜色与图例完全一致
    const colorMap = {
      可用频率: '#b0cf95',
      禁用频率: '#b95f29',
      冲突频率: '#ea3423',
      预留频率: '#385391',
      保护频率: '#f5c242'
    }

    // 准备所有频段数据
    const rangeMap = {
      可用频率: available,
      禁用频率: disabled,
      冲突频率: conflict,
      预留频率: reserved,
      保护频率: protect
    }

    // 生成系列数据
    const seriesData = props.legendData.map(name => {
      // 获取此系列的颜色
      const color = colorMap[name]

      return {
        name,
        type: 'custom',
        // 不需要在这里设置 itemStyle，由全局 color 数组控制
        renderItem: (params, api) => {
          const categoryIndex = api.value(0)
          const start = api.value(1)
          const end = api.value(2)

          const height = api.size([0, 1])[1] * 1 // 控制条形高度
          const x0 = api.coord([start, categoryIndex])[0]
          const y0 = api.coord([0, categoryIndex])[1] - height / 2
          const x1 = api.coord([end, categoryIndex])[0]
          const y1 = y0 + height

          const rectShape = {
            x: x0,
            y: y0,
            width: x1 - x0,
            height: height
          }

          return {
            type: 'rect',
            shape: rectShape,
            style: {
              fill: color // 直接使用对应的颜色，而不是通过 api.style()
            }
          }
        },
        encode: {
          x: [1, 2],
          y: 0
        },
        data: rangeMap[name].map((range, idx) => {
          if (range[0] === 0 && range[1] === 0) {
            return [idx, 0, 0] // 空数据
          }
          return [idx, range[0], range[1]]
        })
      }
    })

    // 设置全局颜色，确保图例与数据系列颜色一致
    const colors = props.legendData.map(name => colorMap[name] || '#ccc')

    return {
      color: colors, // 设置全局颜色数组，与图例顺序一致
      tooltip: {
        formatter: params => {
          const seriesName = params.seriesName
          const idx = params.dataIndex
          const data = params.data

          if (data[1] === 0 && data[2] === 0) {
            return ''
          }

          // 单位格式化：小于1000用MHz，否则用GHz，两位小数
          const fmt = v => (v >= 1000 ? (v / 1000).toFixed(2) + 'GHz' : v.toFixed(0) + 'MHz')

          return `${seriesName}<br/>${fmt(data[1])} - ${fmt(data[2])}`
        }
      },
      legend: {
        show: props.showLegend,
        data: props.legendData,
        bottom: 0,
        itemGap: 12
      },
      grid: { left: 100, right: 40, top: 40, bottom: 60, containLabel: true },
      xAxis: {
        type: 'value',
        min: props.xAxisMin / 1e6,
        max: props.xAxisMax / 1e6,
        splitLine: { show: true },
        axisLabel: {
          formatter: v => {
            if (v >= 1000) return (v / 1000).toFixed(0) + 'GHz'
            return v.toFixed(0) + 'MHz'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisTick: { show: false },
        axisLine: { show: false },
        splitLine: { show: true },
        axisLabel: {
          show: props.showYAxisLabel,
          color: '#666'
        }
      },
      series: seriesData
    }
  }

  // 初始化和更新图表
  const renderChart = () => {
    if (!chartRef.value) return

    // 先把 Hz 转为 MHz
    const toMHz = arr => arr.map(([s, e]) => [s / 1e6, e / 1e6])

    nextTick(() => {
      if (!chartInstance) {
        chartInstance = echarts.init(chartRef.value)
        window.addEventListener('resize', resizeChart)
      }

      const cat = props.categories
      const av = toMHz(props.availableRanges)
      const dis = toMHz(props.disabledRanges)
      const cof = toMHz(props.conflictRanges)
      const res = toMHz(props.reservedRanges)
      const pro = toMHz(props.protectedRanges)

      chartInstance.setOption(makeOption(cat, av, dis, cof, res, pro))
    })
  }

  // 窗口尺寸变化重新调整
  function resizeChart() {
    chartInstance?.resize()
  }

  // 监听props变化，更新图表
  watch(() => props.categories, renderChart, { deep: true })
  watch(() => props.availableRanges, renderChart, { deep: true })
  watch(() => props.disabledRanges, renderChart, { deep: true })
  watch(() => props.conflictRanges, renderChart, { deep: true })
  watch(() => props.reservedRanges, renderChart, { deep: true })
  watch(() => props.protectedRanges, renderChart, { deep: true })
  watch(() => props.legendData, renderChart, { deep: true })

  // 组件挂载时初始化
  onMounted(renderChart)
</script>

<style scoped>
  .freq-chart {
    width: 100%;
    height: 360px;
  }
</style>
