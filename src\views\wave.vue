<script setup>
import { ref, onMounted, reactive } from "vue";
import CubicSpline from "cubic-spline";

const canvasRef = ref(null);

const form = reactive({
  startPoint: 0,
  endPoint: 2047,
  waveform: "正弦波",
});

let waveform = { x: [], y: [] };
let tempControlPoint = null;
let isDragging = false;

const margin = { top: 20, left: 50, right: 20, bottom: 40 };
const yMin = -1,
  yMax = 1;

const dataToScreenX = (x, width) => margin.left + (x / form.endPoint) * (width - margin.left - margin.right);
const dataToScreenY = (y, height) => margin.top + ((yMax - y) / (yMax - yMin)) * (height - margin.top - margin.bottom);
const screenToDataX = (sx, width) => ((sx - margin.left) / (width - margin.left - margin.right)) * form.endPoint;

const generateWaveform = () => {
  waveform.x = [];
  waveform.y = [];
  for (let i = form.startPoint; i <= form.endPoint; i++) {
    waveform.x.push(i);
    waveform.y.push(Math.sin((2 * Math.PI * i) / 2048));
  }
};

const drawWaveform = () => {
  const canvas = canvasRef.value;
  const ctx = canvas.getContext("2d");
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.beginPath();
  ctx.strokeStyle = "#000";

  waveform.x.forEach((x, i) => {
    const sx = dataToScreenX(x, canvas.width);
    const sy = dataToScreenY(waveform.y[i], canvas.height);
    if (i === 0) ctx.moveTo(sx, sy);
    else ctx.lineTo(sx, sy);
  });

  ctx.stroke();

  if (tempControlPoint) {
    ctx.fillStyle = "red";
    ctx.beginPath();
    ctx.arc(tempControlPoint.sx, tempControlPoint.sy, 4, 0, 2 * Math.PI);
    ctx.fill();
  }
};

const updateWaveformWithControlPoint = () => {
  if (!tempControlPoint) return;
  const index = Math.round(tempControlPoint.x);
  waveform.y[index] = tempControlPoint.y;
};

const findClosestYOnWaveform = (x) => {
  const index = Math.round(x);
  return waveform.y[index] || 0;
};

const onMouseDown = (e) => {
  const rect = canvasRef.value.getBoundingClientRect();
  const sx = e.clientX - rect.left;
  const sy = e.clientY - rect.top;
  const xData = screenToDataX(sx, canvasRef.value.width);
  const yData = findClosestYOnWaveform(xData);
  const closestSy = dataToScreenY(yData, canvasRef.value.height);

  tempControlPoint = {
    sx,
    sy: closestSy,
    x: xData,
    y: yData,
  };

  isDragging = true;
  drawWaveform();
};

const onMouseMove = (e) => {
  if (!isDragging || !tempControlPoint) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const sx = e.clientX - rect.left;

  tempControlPoint.sx = sx;
  tempControlPoint.x = screenToDataX(sx, canvasRef.value.width);
  tempControlPoint.y = Math.max(
    Math.min(
      ((margin.top + canvasRef.value.height - margin.bottom - (e.clientY - rect.top)) /
        (canvasRef.value.height - margin.top - margin.bottom)) *
        2 -
        1,
      1
    ),
    -1
  );
  tempControlPoint.sy = dataToScreenY(tempControlPoint.y, canvasRef.value.height);

  updateWaveformWithControlPoint();
  drawWaveform();
};

const onMouseUp = () => {
  if (isDragging) {
    isDragging = false;
    tempControlPoint = null;
    drawWaveform();
  }
};

onMounted(() => {
  const canvas = canvasRef.value;
  canvas.width = canvas.offsetWidth;
  canvas.height = canvas.offsetHeight;
  generateWaveform();
  drawWaveform();
  canvas.addEventListener("mousedown", onMouseDown);
  canvas.addEventListener("mousemove", onMouseMove);
  canvas.addEventListener("mouseup", onMouseUp);
  canvas.addEventListener("mouseleave", onMouseUp);
});
</script>

<template>
  <canvas ref="canvasRef" style="border: 1px solid #ccc; width: 100%; height: 400px"></canvas>
</template>

<style scoped>
canvas {
  background: #fff;
}
</style>
