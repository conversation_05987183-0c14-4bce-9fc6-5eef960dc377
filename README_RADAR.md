# Cesium 雷达覆盖范围可视化

## 功能概述

这个组件实现了基于 Cesium 的 3D 雷达覆盖范围可视化，具有以下特性：

### 主要功能
1. **半球形雷达覆盖范围** - 使用半透明的椭球体表示雷达的最大探测范围
2. **圆锥体扫描效果** - 内部圆锥体以雷达中心为轴心进行360度循环扫描
3. **实时参数调整** - 支持动态调整雷达参数和位置
4. **3D 地球视图** - 基于真实地形的三维可视化

### 视觉效果
- **半球覆盖范围**: 青色半透明椭球，表示雷达的最大探测距离
- **扇形扫描区域**: 黄色半透明扇形，模拟雷达的扫描覆盖范围
- **多波束扫描**: 绿色渐变波束，模拟真实雷达的多波束扫描
- **主扫描线**: 红色细线，表示当前扫描方向
- **动态旋转**: 扇形区域以设定的速度进行连续旋转扫描

## 控制面板功能

### 雷达控制
- **开始/停止扫描**: 控制雷达扫描动画的启动和停止

### 雷达参数
- **覆盖半径**: 调整雷达的探测距离（10-100公里）
- **扫描速度**: 设置扇形的旋转速度（0.5-10度/秒）
- **扇形角度**: 调整雷达扫描的角度范围（15-120度）
- **经纬度**: 设置雷达站的地理位置
- **高度**: 设置雷达站的海拔高度
- **更新位置**: 应用新的地理位置设置

## 技术实现

### 核心技术栈
- **Cesium**: 3D 地球和地理空间可视化
- **Vue 3**: 响应式前端框架
- **Element Plus**: UI 组件库

### 关键实现
1. **半球创建**: 使用 `Cesium.EllipsoidGraphics` 创建半球形覆盖范围
2. **扇形扫描**: 使用 `Cesium.CylinderGraphics` 创建可调角度的扇形区域
3. **多波束效果**: 创建多个细波束模拟真实雷达扫描
4. **主扫描线**: 红色细线指示当前扫描方向
5. **动画系统**: 使用 `requestAnimationFrame` 实现平滑的旋转动画
6. **动态属性**: 使用 `Cesium.CallbackProperty` 实现实时更新

### 代码结构
```
SkyWaveSingleLink.vue
├── 脚本逻辑
│   ├── Cesium 初始化
│   ├── 雷达实体创建
│   ├── 扫描动画控制
│   └── 参数更新函数
├── 模板结构
│   ├── 控制面板
│   └── Cesium 容器
└── 样式定义
    ├── 控制面板样式
    └── Cesium 容器样式
```

## 使用说明

1. **启动应用**: 访问对应的路由页面
2. **查看雷达**: 页面加载后会自动显示默认位置（北京）的雷达覆盖范围
3. **开始扫描**: 点击"开始扫描"按钮启动雷达扫描动画
4. **调整参数**: 使用左侧控制面板调整雷达的各项参数
5. **更改位置**: 修改经纬度后点击"更新位置"应用新位置

## 自定义配置

### 默认参数
- 位置: 北京 (116.3974°E, 39.9093°N)
- 高度: 100米
- 覆盖半径: 50公里
- 扫描速度: 2度/秒
- 扇形角度: 60度扇形

### 可调整范围
- 覆盖半径: 10-100公里
- 扫描速度: 0.5-10度/秒
- 高度: 0-10000米
- 位置: 全球任意坐标

## 扩展功能建议

1. **多雷达支持**: 支持同时显示多个雷达站
2. **目标检测**: 在雷达范围内显示检测到的目标
3. **地形遮挡**: 考虑地形对雷达覆盖的影响
4. **数据导入**: 支持从文件导入雷达配置
5. **导出功能**: 支持导出雷达覆盖分析结果

## 性能优化

- 使用 `requestAnimationFrame` 确保动画流畅
- 合理设置椭球和圆锥的分段数以平衡性能和视觉效果
- 在组件卸载时正确清理资源和动画
