<!-- components/SpecChart/index.vue -->
<template>
  <div ref="chartRef" class="freq-chart" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick, defineExpose } from "vue";
import * as echarts from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { TooltipComponent, GridComponent, LegendComponent } from "echarts/components";
import { CustomChart } from "echarts/charts";

// 注册 ECharts 模块
echarts.use([CanvasRenderer, TooltipComponent, GridComponent, LegendComponent, CustomChart]);

// 定义 Props
const props = defineProps({
  categories: { type: Array, default: () => [] },
  // 每个都是：Array<[startHz, endHz]> ；长度应和 categories 对应
  availableRanges: { type: Array, default: () => [] },
  disabledRanges: { type: Array, default: () => [] },
  conflictRanges: { type: Array, default: () => [] },
  reservedRanges: { type: Array, default: () => [] },
  protectedRanges: { type: Array, default: () => [] },

  xAxisMin: { type: Number, default: 30e6 },
  xAxisMax: { type: Number, default: 18e9 },

  showLegend: { type: Boolean, default: true },
  legendData: {
    type: Array,
    default: () => ["可用频率", "禁用频率", "冲突频率", "预留频率", "空闲频率"],
  },
  showYAxisLabel: { type: Boolean, default: true },
});

const chartRef = ref(null);
let chartInstance = null;

// 色彩映射
const colorMap = {
  可用频率: "#829549",
  禁用频率: "#b95f29",
  冲突频率: "#e50000",
  预留频率: "#ab9dc3",
  空闲频率: "#eeee4d",
};

// —— 工具：扁平化＆转 MHz —————————————————————————————
function flattenAndToMHz(arr2d) {
  return arr2d.flatMap((ranges, idx) =>
    (ranges || [])
      .filter(([s, e]) => s < e) // 去掉无效段
      .map(([s, e]) => [idx, s / 1e6, e / 1e6])
  );
}

// —— 构建 option —————————————————————————————————————
function makeOption() {
  // 将所有分类的「Hz 段」一次性扁平化
  const avData = flattenAndToMHz(props.availableRanges);
  const disData = flattenAndToMHz(props.disabledRanges);
  const cofData = flattenAndToMHz(props.conflictRanges);
  const resData = flattenAndToMHz(props.reservedRanges);
  const proData = flattenAndToMHz(props.protectedRanges);

  // 为每个图例项生成 custom 系列
  const series = props.legendData.map((name) => {
    // 选取对应扁平化后的 data
    let raw = [];
    switch (name) {
      case "可用频率":
        raw = avData;
        break;
      case "禁用频率":
        raw = disData;
        break;
      case "冲突频率":
        raw = cofData;
        break;
      case "预留频率":
        raw = resData;
        break;
      case "空闲频率":
        raw = proData;
        break;
    }
    return {
      name,
      type: "custom",
      yAxisIndex: 0,
      // renderItem：用整行高度
      renderItem: (params, api) => {
        const catIdx = api.value(0); // 类别索引
        const start = api.value(1); // 起始频段
        const end = api.value(2); // 结束频段

        // 控制矩形高度为总高度的80%
        const hei = api.size([0, 1])[1] * 0.8;

        // 计算矩形的坐标（起始、结束位置）
        const x0 = api.coord([start, catIdx])[0];
        const y0 = api.coord([0, catIdx])[1] - hei / 2;
        const x1 = api.coord([end, catIdx])[0];

        // 矩形内显示的文字（频段范围）
        let text;

        switch (name) {
          case "预留频率":
            text = (start / 1000).toFixed(3) + "GHz" + " - " + (end / 1000).toFixed(3) + "GHz"; // 显示预留频段的范围
            break;
          case "空闲频率":
            text = "空闲"; // 只显示“空闲”
            break;
          default:
            text = ((start + 10 / 2) / 1000).toFixed(3) + "GHz"; // 默认情况下显示频段起点
            break;
        }

        // 返回矩形和文本作为有效的图形对象
        return {
          type: "group", // 使用 group 来返回多个图形对象
          children: [
            {
              type: "rect", // 绘制矩形
              shape: { x: x0, y: y0, width: x1 - x0, height: hei }, // 矩形的形状
              style: { fill: colorMap[name] }, // 矩形的填充颜色
            },
            {
              type: "text", // 绘制文本
              style: {
                x: (x0 + x1) / 2, // 文本水平居中
                y: y0 + hei / 2, // 文本垂直居中
                text: text, // 显示的文本内容
                textAlign: "center", // 文本水平居中
                textVerticalAlign: "middle", // 文本垂直居中
                fill: "#fff", // 文本颜色
                font: "12px Arial", // 字体大小和样式
              },
            },
          ],
        };
      },

      encode: { x: [1, 2], y: 0 },
      data: raw,
    };
  });

  return {
    color: props.legendData.map((n) => colorMap[n] || "#ccc"),

    tooltip: {
      formatter: (p) => {
        if (!p.data) return "";
        const fmt = (v) => (v >= 1000 ? (v / 1000).toFixed(3) + "GHz" : v.toFixed(3) + "MHz");
        return `${p.seriesName}<br/>${fmt(p.data[1])} - ${fmt(p.data[2])}`;
      },
    },

    legend: {
      show: props.showLegend,
      data: props.legendData,
      bottom: 0,
      itemGap: 24, // 图例项间隔
    },

    grid: { left: 100, right: 40, top: 40, bottom: 60, containLabel: true },

    xAxis: {
      type: "value",
      min: props.xAxisMin / 1e6,
      max: props.xAxisMax / 1e6,
      splitLine: { show: true, lineStyle: { color: "#666" } },
      axisLabel: {
        formatter: (v) => (v >= 1000 ? (v / 1000).toFixed(3) + "GHz" : v.toFixed(3) + "MHz"),
      },
      axisLine: { show: false },
      splitNumber: 20,
    },

    yAxis: {
      type: "category",
      data: props.categories,
      boundaryGap: [0, 0], // 让最顶部和最底部也有网格
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { show: props.showYAxisLabel, color: "#666" },
      splitLine: { show: true, lineStyle: { color: "#666" } },
    },
    series,
  };
}

// —— 渲染 & 暴露给父组件调用 —————————————————————————
function renderChart() {
  nextTick(() => {
    if (!chartInstance && chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener("resize", () => chartInstance.resize());
    }
    chartInstance.setOption(makeOption(), true);
  });
}
defineExpose({ renderChart });

// —— 生命周期 ————————————————————————————————————————
onMounted(renderChart);
onBeforeUnmount(() => {
  chartInstance?.dispose();
  chartInstance = null;
});
// 监听 props 改变自动重绘
watch(
  () => [
    props.categories,
    props.availableRanges,
    props.disabledRanges,
    props.conflictRanges,
    props.reservedRanges,
    props.protectedRanges,
    props.legendData,
    props.showLegend,
    props.showYAxisLabel,
    props.xAxisMin,
    props.xAxisMax,
  ],
  renderChart,
  { deep: true }
);
</script>

<style scoped>
.freq-chart {
  width: 100%;
  height: 360px;
}
</style>
