<script setup>
import * as Cesium from "cesium";
import { EllipsoidTerrainProvider, UrlTemplateImageryProvider, Cartesian3, Color, Rectangle } from "cesium";
import { onMounted, onUnmounted, ref } from "vue";

// Cesium相关变量
const cesiumContainer = ref(null);
let viewer = null;
let radarEntity = null;
let scanConeEntity = null;
let animationId = null;
let scanAngle = 0;

// 雷达参数
const radarParams = ref({
  position: { longitude: 116.3974, latitude: 39.9093, height: 100 }, // 北京坐标
  radius: 50000, // 雷达覆盖半径 50km
  maxElevation: 90, // 最大仰角
  scanSpeed: 3, // 扫描速度（度/秒）
  scanAngle: 45, // 扇形扫描角度
  sweepMode: "continuous", // 扫描模式：continuous(连续) 或 sector(扇区)
  isScanning: false,
});

// 初始化Cesium
const initCesium = () => {
  if (!cesiumContainer.value) return;

  // 创建Cesium Viewer
  viewer = new Cesium.Viewer(cesiumContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    geocoder: false,
    infoBox: false,
    selectionIndicator: false,
  });

  // 设置初始视角
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      radarParams.value.position.longitude,
      radarParams.value.position.latitude,
      radarParams.value.position.height + 80000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0,
    },
  });

  createRadarCoverage();
};

// 创建雷达覆盖范围
const createRadarCoverage = () => {
  const position = Cesium.Cartesian3.fromDegrees(
    radarParams.value.position.longitude,
    radarParams.value.position.latitude,
    radarParams.value.position.height
  );

  // 创建半球形雷达覆盖范围
  radarEntity = viewer.entities.add({
    position: position,
    ellipsoid: {
      radii: new Cesium.Cartesian3(radarParams.value.radius, radarParams.value.radius, radarParams.value.radius),
      material: Cesium.Color.CYAN.withAlpha(0.2),
      outline: true,
      outlineColor: Cesium.Color.CYAN.withAlpha(0.8),
      slicePartitions: 32,
      stackPartitions: 16,
      // 只显示上半球
      maximumCone: Cesium.Math.toRadians(90),
    },
  });

  // 创建扫描圆锥体
  createScanCone(position);

  // 创建距离环
  createRangeRings(position);
};

// 创建扇面扫描区域
const createScanCone = (position) => {
  // 创建扇面扫描效果 - 类似雷达屏幕上的扫描扇面
  const scanAngleRad = Cesium.Math.toRadians(radarParams.value.scanAngle);

  // 创建扫描扇面
  scanConeEntity = viewer.entities.add({
    polygon: {
      hierarchy: new Cesium.CallbackProperty(() => {
        const currentPositions = [];
        const currentAngleRad = Cesium.Math.toRadians(scanAngle);
        const steps = 64; // 扇面的分段数，更多分段使扇面更平滑

        // 扇面中心点
        currentPositions.push(position);

        // 生成扇面 - 从当前角度开始，向后扫描一定角度
        const sweepAngle = scanAngleRad; // 扫描扇面的角度范围

        for (let i = 0; i <= steps; i++) {
          // 从当前角度向后扫描
          const angle = currentAngleRad - sweepAngle + (sweepAngle * i) / steps;
          const x = radarParams.value.radius * Math.cos(angle);
          const y = radarParams.value.radius * Math.sin(angle);

          const pointPosition = Cesium.Cartesian3.fromDegrees(
            radarParams.value.position.longitude + x / 111320,
            radarParams.value.position.latitude + y / 111320,
            radarParams.value.position.height
          );
          currentPositions.push(pointPosition);
        }

        return new Cesium.PolygonHierarchy(currentPositions);
      }, false),
      // 使用固定颜色材质
      material: Cesium.Color.LIME.withAlpha(0.4),
      outline: false,
      height: radarParams.value.position.height + 10,
      extrudedHeight: radarParams.value.position.height + 1000,
    },
  });

  // 添加多层扫描扇面效果
  createMultiLayerSweep(position);

  // 添加扫描线效果
  createScanBeam(position);
};

// 创建扫描线
const createScanBeam = (position) => {
  // 创建主扫描线 - 当前扫描方向的亮线
  const mainBeamEntity = viewer.entities.add({
    polyline: {
      positions: new Cesium.CallbackProperty(() => {
        const currentAngleRad = Cesium.Math.toRadians(scanAngle);
        const endX = radarParams.value.radius * Math.cos(currentAngleRad);
        const endY = radarParams.value.radius * Math.sin(currentAngleRad);

        const endPosition = Cesium.Cartesian3.fromDegrees(
          radarParams.value.position.longitude + endX / 111320,
          radarParams.value.position.latitude + endY / 111320,
          radarParams.value.position.height + 500
        );

        return [position, endPosition];
      }, false),
      width: 4,
      material: Cesium.Color.YELLOW.withAlpha(1.0), // 亮黄色扫描线
      clampToGround: false,
    },
  });

  // 创建扫描扇面的前沿线（最亮的边缘）
  const sweepEdgeEntity = viewer.entities.add({
    polyline: {
      positions: new Cesium.CallbackProperty(() => {
        const currentAngleRad = Cesium.Math.toRadians(scanAngle);
        const steps = 32;
        const positions = [];

        // 创建扇面前沿的弧线
        for (let i = 0; i <= steps; i++) {
          const angle =
            currentAngleRad -
            Cesium.Math.toRadians(radarParams.value.scanAngle) +
            (Cesium.Math.toRadians(radarParams.value.scanAngle) * i) / steps;
          const x = radarParams.value.radius * Math.cos(angle);
          const y = radarParams.value.radius * Math.sin(angle);

          const pointPosition = Cesium.Cartesian3.fromDegrees(
            radarParams.value.position.longitude + x / 111320,
            radarParams.value.position.latitude + y / 111320,
            radarParams.value.position.height + 500
          );
          positions.push(pointPosition);
        }

        return positions;
      }, false),
      width: 3,
      material: Cesium.Color.LIME.withAlpha(0.9), // 绿色扇面边缘
      clampToGround: false,
    },
  });

  // 将所有扫描线添加到数组中以便管理
  if (!viewer.scanBeams) viewer.scanBeams = [];
  viewer.scanBeams.push(mainBeamEntity, sweepEdgeEntity);
};

// 创建简化的扫描扇面效果
const createMultiLayerSweep = (position) => {
  // 创建单层扫描扇面，避免材质错误
  const sweepEntity = viewer.entities.add({
    polygon: {
      hierarchy: new Cesium.CallbackProperty(() => {
        const currentPositions = [];
        const currentAngleRad = Cesium.Math.toRadians(scanAngle);
        const sweepAngleRad = Cesium.Math.toRadians(radarParams.value.scanAngle * 0.8);
        const steps = 32;

        // 扇面中心点
        currentPositions.push(position);

        // 生成扫描扇面
        for (let i = 0; i <= steps; i++) {
          const angle = currentAngleRad - sweepAngleRad + (sweepAngleRad * i) / steps;
          const x = radarParams.value.radius * Math.cos(angle);
          const y = radarParams.value.radius * Math.sin(angle);

          const pointPosition = Cesium.Cartesian3.fromDegrees(
            radarParams.value.position.longitude + x / 111320,
            radarParams.value.position.latitude + y / 111320,
            radarParams.value.position.height
          );
          currentPositions.push(pointPosition);
        }

        return new Cesium.PolygonHierarchy(currentPositions);
      }, false),
      material: Cesium.Color.LIME.withAlpha(0.25),
      outline: false,
      height: radarParams.value.position.height + 10,
      extrudedHeight: radarParams.value.position.height + 600,
    },
  });

  // 将扫描扇面添加到管理数组
  if (!viewer.scanBeams) viewer.scanBeams = [];
  viewer.scanBeams.push(sweepEntity);
};

// 创建距离环
const createRangeRings = (position) => {
  const ringCount = 4; // 距离环数量
  const ringStep = radarParams.value.radius / ringCount;

  for (let i = 1; i <= ringCount; i++) {
    const ringRadius = ringStep * i;

    // 创建距离环
    const ringEntity = viewer.entities.add({
      position: position,
      ellipse: {
        semiMajorAxis: ringRadius,
        semiMinorAxis: ringRadius,
        height: radarParams.value.position.height + 10,
        material: Cesium.Color.CYAN.withAlpha(0.3),
        outline: true,
        outlineColor: Cesium.Color.CYAN.withAlpha(0.6),
        outlineWidth: 1,
      },
    });

    // 添加距离标签
    const labelEntity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        radarParams.value.position.longitude + ringRadius / 111320,
        radarParams.value.position.latitude,
        radarParams.value.position.height + 100
      ),
      label: {
        text: `${(ringRadius / 1000).toFixed(0)}km`,
        font: "12pt sans-serif",
        fillColor: Cesium.Color.CYAN,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -10),
      },
    });

    // 将距离环添加到管理数组
    if (!viewer.rangeRings) viewer.rangeRings = [];
    viewer.rangeRings.push(ringEntity, labelEntity);
  }
};

// 开始/停止扫描动画
const toggleScan = () => {
  radarParams.value.isScanning = !radarParams.value.isScanning;

  if (radarParams.value.isScanning) {
    startScanAnimation();
  } else {
    stopScanAnimation();
  }
};

// 开始扫描动画
const startScanAnimation = () => {
  const animate = () => {
    if (!radarParams.value.isScanning) return;

    scanAngle += radarParams.value.scanSpeed;
    if (scanAngle >= 360) {
      scanAngle = 0;
    }

    animationId = requestAnimationFrame(animate);
  };

  animate();
};

// 停止扫描动画
const stopScanAnimation = () => {
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }
};

// 更新雷达参数
const updateRadarParams = () => {
  if (!viewer || !radarEntity || !scanConeEntity) return;

  // 更新半球覆盖范围
  radarEntity.ellipsoid.radii = new Cesium.Cartesian3(
    radarParams.value.radius,
    radarParams.value.radius,
    radarParams.value.radius
  );

  // 更新扇形的高度
  scanConeEntity.polygon.extrudedHeight = radarParams.value.position.height + 1000;

  // 重新创建扫描波束以适应新的角度和半径
  recreateScanBeams();
};

// 重新创建扫描波束
const recreateScanBeams = () => {
  // 清除现有的扫描波束
  if (viewer.scanBeams) {
    viewer.scanBeams.forEach((beam) => viewer.entities.remove(beam));
    viewer.scanBeams = [];
  }

  // 清除现有的距离环
  if (viewer.rangeRings) {
    viewer.rangeRings.forEach((ring) => viewer.entities.remove(ring));
    viewer.rangeRings = [];
  }

  // 重新创建扫描波束和距离环
  const position = Cesium.Cartesian3.fromDegrees(
    radarParams.value.position.longitude,
    radarParams.value.position.latitude,
    radarParams.value.position.height
  );
  createScanBeam(position);
  createRangeRings(position);
};

// 更新雷达位置
const updateRadarPosition = () => {
  if (!viewer || !radarEntity || !scanConeEntity) return;

  const newPosition = Cesium.Cartesian3.fromDegrees(
    radarParams.value.position.longitude,
    radarParams.value.position.latitude,
    radarParams.value.position.height
  );

  // 更新雷达实体位置
  radarEntity.position = newPosition;
  scanConeEntity.position = newPosition;

  // 更新相机视角
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      radarParams.value.position.longitude,
      radarParams.value.position.latitude,
      radarParams.value.position.height + 80000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0,
    },
  });
};

// 生命周期钩子
onMounted(() => {
  initCesium();
});

onUnmounted(() => {
  stopScanAnimation();

  // 清理扫描波束
  if (viewer && viewer.scanBeams) {
    viewer.scanBeams.forEach((beam) => viewer.entities.remove(beam));
    viewer.scanBeams = [];
  }

  // 清理距离环
  if (viewer && viewer.rangeRings) {
    viewer.rangeRings.forEach((ring) => viewer.entities.remove(ring));
    viewer.rangeRings = [];
  }

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
});
</script>

<template>
  <div class="radar-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <h3>雷达控制</h3>
        <el-button :type="radarParams.isScanning ? 'danger' : 'primary'" @click="toggleScan">
          {{ radarParams.isScanning ? "停止扫描" : "开始扫描" }}
        </el-button>
      </div>

      <div class="control-group">
        <h4>雷达参数</h4>
        <div class="param-item">
          <label>覆盖半径 (km):</label>
          <el-slider
            v-model="radarParams.radius"
            :min="10000"
            :max="100000"
            :step="5000"
            :format-tooltip="(val) => val / 1000 + 'km'"
            @change="updateRadarParams"
          />
        </div>

        <div class="param-item">
          <label>扫描速度 (度/秒):</label>
          <el-slider
            v-model="radarParams.scanSpeed"
            :min="0.5"
            :max="10"
            :step="0.5"
            :format-tooltip="(val) => val + '°/s'"
          />
        </div>

        <div class="param-item">
          <label>扇形角度 (度):</label>
          <el-slider
            v-model="radarParams.scanAngle"
            :min="15"
            :max="120"
            :step="5"
            :format-tooltip="(val) => val + '°'"
            @change="updateRadarParams"
          />
        </div>

        <div class="param-item">
          <label>扫描模式:</label>
          <el-select v-model="radarParams.sweepMode" size="small">
            <el-option label="连续扫描" value="continuous" />
            <el-option label="扇区扫描" value="sector" />
          </el-select>
        </div>

        <div class="param-item">
          <label>经度:</label>
          <el-input-number v-model="radarParams.position.longitude" :precision="4" :step="0.0001" size="small" />
        </div>

        <div class="param-item">
          <label>纬度:</label>
          <el-input-number v-model="radarParams.position.latitude" :precision="4" :step="0.0001" size="small" />
        </div>

        <div class="param-item">
          <label>高度 (m):</label>
          <el-input-number v-model="radarParams.position.height" :min="0" :max="10000" :step="10" size="small" />
        </div>

        <el-button @click="updateRadarPosition" type="success" size="small"> 更新位置 </el-button>
      </div>
    </div>

    <!-- Cesium 3D 视图 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
  </div>
</template>

<style lang="scss" scoped>
.radar-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background: #000;
}

.control-panel {
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid #333;

  h3 {
    color: #00ffff;
    margin-bottom: 15px;
    font-size: 18px;
  }

  h4 {
    color: #ffff00;
    margin: 20px 0 10px 0;
    font-size: 14px;
  }
}

.control-group {
  margin-bottom: 25px;

  .el-button {
    width: 100%;
    margin-bottom: 10px;
  }
}

.param-item {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #ccc;
  }

  .el-slider {
    margin: 10px 0;
  }

  .el-input-number {
    width: 100%;
  }
}

.cesium-container {
  flex: 1;
  height: 100%;
  position: relative;
}

// Cesium 样式覆盖
:deep(.cesium-viewer) {
  font-family: sans-serif;
}

:deep(.cesium-viewer-cesiumWidgetContainer) {
  background: #000;
}

:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  background: rgba(42, 42, 42, 0.8);
}
</style>
