<template>
  <div ref="mapContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import {
  PolylineCollection,
  Polyline,
  Color,
  Cartesian3,
  Transforms,
  EllipsoidTerrainProvider,
  UrlTemplateImageryProvider,
  MaterialAppearance,
  Viewer,
} from "cesium";
import { radarDetectionArea } from "@/utils/radarPower.js";

const mapContainer = ref(null);
let viewer = null;

function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";

  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );
}

// 改 computeSurfacePoints，返回二维数组
function computeSurfacePoints2D(radarLon, radarLat, radarHeight, R_max, nRow, nCol) {
  // 1) 雷达中心点的世界坐标
  const radarCartesian = Cartesian3.fromDegrees(radarLon, radarLat, radarHeight);
  // 2) 从 ENU 局部坐标系到世界坐标系的转换矩阵
  const enuMatrix = Transforms.eastNorthUpToFixedFrame(radarCartesian);

  // 3) 准备一个二维数组 positions2D[i][j]
  const positions2D = Array(nRow);
  for (let i = 0; i < nRow; i++) {
    positions2D[i] = new Array(nCol);
    // 仰角 i 对应 0～180° 之间
    const eleRad = Cesium.Math.toRadians((i / (nRow - 1)) * 180);
    const cosE = Math.cos(eleRad);
    const sinE = Math.sin(eleRad);

    for (let j = 0; j < nCol; j++) {
      // 方位 j 对应 0～360° 之间
      const azRad = Cesium.Math.toRadians((j / (nCol - 1)) * 360);
      const range = R_max[i][j]; // 该方向上的探测距离（米）

      // 在局部 ENU 坐标系里计算点
      //   x = range * cosE * sin(az)
      //   y = range * cosE * cos(az)
      //   z = range * sinE
      const local = new Cartesian3(range * cosE * Math.sin(azRad), range * cosE * Math.cos(azRad), range * sinE);

      // 把 ENU 坐标 “放” 回地球世界坐标（ECEF）
      const world = new Cartesian3();
      Cesium.Matrix4.multiplyByPoint(enuMatrix, local, world);
      positions2D[i][j] = world;
    }
  }
  return positions2D;
}

function draw3DRadarCoverage({ radarLon, radarLat, radarHeight, R_max, nRow, nCol }) {
  // 先清空旧的线条
  viewer.scene.primitives.removeAll();

  // 1) 计算所有点的世界坐标
  const grid = computeSurfacePoints2D(radarLon, radarLat, radarHeight, R_max, nRow, nCol);

  // 2) 准备一个半透明黄色的线材质
  const lineAppearance = new Cesium.PolylineMaterialAppearance({
    material: Cesium.Material.fromType("Color", { color: Color.YELLOW.withAlpha(0.4) }),
  });

  // 3) 画“等仰角环”——对每一行 i，把 grid[i][0..nCol-1] 连成闭合圈
  for (let i = 0; i < nRow; i++) {
    const pts = grid[i].slice(); // 复制该行所有点
    pts.push(grid[i][0]); // 最后再回到第一个点以闭合
    const geom = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({ geometry: geom }),
        appearance: lineAppearance,
        asynchronous: false,
      })
    );
  }

  // 4) 画“方位径向线”——对每一列 j，把 grid[0..nRow-1][j] 从中心向外连成线
  for (let j = 0; j < nCol; j++) {
    const pts = [];
    for (let i = 0; i < nRow; i++) {
      pts.push(grid[i][j]);
    }
    const geom = new Cesium.PolylineGeometry({ positions: pts, width: 1 });
    viewer.scene.primitives.add(
      new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({ geometry: geom }),
        appearance: lineAppearance,
        asynchronous: false,
      })
    );
  }
}

// 在 onMounted 中调用
onMounted(() => {
  initMap();
  const { nRow, nCol, R_max } = radarDetectionArea.data;
  draw3DRadarCoverage({
    radarLon: 118.8,
    radarLat: 32.05,
    radarHeight: 50,
    R_max,
    nRow,
    nCol,
  });
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(118.8, 32.05, 50000),
    orientation: { heading: 0, pitch: -Math.PI / 4, roll: 0 },
  });
});

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
});
</script>
