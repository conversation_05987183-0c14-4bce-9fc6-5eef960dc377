# 空间测距功能说明

## 功能概述

在原有的平面测距基础上，新增了空间测距功能，专门用于测量3D空间中两点之间的真实直线距离，特别适用于飞机与塔台、飞机与飞机之间的距离测量。

### 🎯 功能特性

**平面测距**（原有功能）：
- 📏 **地面距离** - 测量地面或贴地的平面距离
- 🔗 **多点连线** - 支持多点连续测距
- 🟠 **橙色标识** - 使用橙色线条和红色点

**空间测距**（新增功能）：
- 🚀 **3D空间距离** - 测量真实的3D空间直线距离
- ✈️ **航空专用** - 专为飞机、塔台等空中目标设计
- 🟣 **紫色标识** - 使用紫色虚线和紫色点进行区分
- 📐 **两点测距** - 简单的两点间直线距离测量

### 🎮 操作方式

**平面测距操作**：
1. 点击"📏 开始平面测距"
2. 在地图上单击添加测距点
3. 继续单击添加更多点
4. 双击或右键结束测距

**空间测距操作**：
1. 点击"🚀 开始空间测距"
2. 点击第一个测量点（如塔台）
3. 点击第二个测量点（如飞机）
4. 自动显示空间距离并结束

### 🛠️ 技术实现

**空间距离计算**：
```javascript
// 计算3D空间中两点的直线距离
const distance = Cesium.Cartesian3.distance(point1, point2)

// 不贴地，保持真实3D位置
heightReference: Cesium.HeightReference.NONE
```

**视觉区分**：
```javascript
// 紫色虚线连接
material: new Cesium.PolylineDashMaterialProperty({
  color: Cesium.Color.PURPLE,
  dashLength: 10
})

// 紫色测距点
point: {
  pixelSize: 10,
  color: Cesium.Color.PURPLE,
  heightReference: Cesium.HeightReference.NONE
}
```

### 🎨 界面设计

**工具栏布局**：
- 📍 位置：右上角浮动工具栏
- 🎨 样式：半透明白色背景，圆角阴影
- 🌈 按钮颜色：
  - 🔵 蓝色：平面测距（未激活）
  - 🟣 紫色：空间测距（未激活）
  - 🔴 红色：测距进行中
  - 🟠 橙色：清除功能

### 📊 显示效果

**平面测距显示**：
- 🔴 红色点标记
- 🟠 橙色实线连接
- 🏷️ 黄色距离标签

**空间测距显示**：
- 🟣 紫色点标记
- 🟣 紫色虚线连接
- 🏷️ 紫色距离标签
- 📐 格式：`空间距离: XX.XX km`

### 🛩️ 演示实体

系统自动添加了演示实体用于测试空间测距：

**控制塔台**：
- 📍 位置：128.0°E, 36.0°N, 100m
- 🗼 图标：灰色塔台图标
- 🏷️ 标签：🗼 控制塔台

**客机A320**：
- 📍 位置：127.8°E, 35.8°N, 3000m
- ✈️ 图标：蓝色客机图标
- 🏷️ 标签：✈️ 客机A320 (3km)

**战斗机F16**：
- 📍 位置：128.2°E, 36.2°N, 8000m
- 🚁 图标：橙色战斗机图标
- 🏷️ 标签：🚁 战斗机F16 (8km)

**救援直升机**：
- 📍 位置：127.9°E, 36.1°N, 1500m
- 🚁 图标：绿色直升机图标
- 🏷️ 标签：🚁 救援直升机 (1.5km)

### 🔧 使用场景

**空间测距适用于**：
- ✈️ **飞机与塔台距离** - 计算飞机到控制塔的直线距离
- 🛩️ **飞机间距离** - 测量两架飞机之间的空中距离
- 🚁 **直升机定位** - 救援直升机与目标的空间距离
- 📡 **雷达覆盖** - 雷达站与目标的3D空间距离
- 🛰️ **卫星通信** - 地面站与卫星的空间距离

**平面测距适用于**：
- 🏗️ 建筑物间距测量
- 🛣️ 道路长度测量
- 📏 地面距离测量
- 🗺️ 路径规划

### 💡 使用技巧

1. **精确点击** - 在3D视图中调整视角，确保点击到正确的目标
2. **高度区分** - 空间测距会显示真实的3D距离，包含高度差
3. **视觉区分** - 紫色表示空间测距，橙色表示平面测距
4. **快速测量** - 空间测距只需两次点击即可完成
5. **清除管理** - 每条测距线都有独立的清除按钮

### 📐 距离对比

以塔台到客机A320为例：
- **平面距离**：约22.4km（地面投影距离）
- **空间距离**：约22.6km（包含3000m高度差的直线距离）

高度差越大，空间距离与平面距离的差异越明显。

### 🎯 技术优势

1. **真实3D计算** - 使用Cesium的3D空间坐标系统
2. **不贴地显示** - 保持目标的真实高度位置
3. **视觉区分** - 清晰的颜色和样式区分不同测距类型
4. **简单操作** - 两次点击即可完成空间距离测量
5. **精确计算** - 考虑地球曲率的精确距离计算

这个空间测距功能完美补充了平面测距，为航空、雷达、卫星等3D应用场景提供了专业的距离测量工具！
