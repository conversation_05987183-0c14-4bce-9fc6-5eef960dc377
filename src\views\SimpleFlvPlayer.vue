<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

// 响应式状态
const videoRef = ref(null);
const isConnected = ref(false);
const isStreaming = ref(false);
const status = ref("未连接");

// WebSocket 和播放器实例
let ws = null;
let player = null;

// 配置
const WS_URL = "ws://***********:9085/ws/video-stream";

// 开始推流
function startStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接，请等待连接建立");
    return;
  }

  // 发送开始指令
  const command = {
    action: "start",
    streamUrl: "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8",
  };

  ws.send(JSON.stringify(command));
  console.log("发送开始指令:", command);

  isStreaming.value = true;
  status.value = "正在请求流...";
}

// 停止推流
function stopStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接");
    return;
  }

  // 发送停止指令
  const command = { action: "stop" };
  ws.send(JSON.stringify(command));
  console.log("发送停止指令:", command);

  // 清理播放器
  cleanupPlayer();
  isStreaming.value = false;
  status.value = "已停止";
}

// 处理 FLV 数据
function handleFlvData(flvData) {
  console.log("收到 FLV 数据，大小:", flvData.byteLength || flvData.length);

  try {
    // 清理旧的播放器
    if (player) {
      cleanupPlayer();
    }

    // 检查 flv.js 支持
    if (!flvjs.isSupported()) {
      console.error("浏览器不支持 flv.js");
      status.value = "浏览器不支持";
      return;
    }

    // 创建 Blob URL
    const blob = new Blob([flvData], { type: "video/x-flv" });
    const url = URL.createObjectURL(blob);

    // 创建播放器
    player = flvjs.createPlayer({
      type: "flv",
      url: url,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      enableWorker: false,
      enableStashBuffer: false,
    });

    // 绑定到视频元素
    player.attachMediaElement(videoRef.value);

    // 监听事件
    player.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      console.error("播放器错误:", errorType, errorDetail, errorInfo);
      status.value = "播放错误";
    });

    player.on(flvjs.Events.LOADING_COMPLETE, () => {
      console.log("加载完成");
    });

    // 加载并播放
    player.load();
    player
      .play()
      .then(() => {
        console.log("开始播放");
        status.value = "正在播放";
      })
      .catch((err) => {
        console.error("播放失败:", err);
        status.value = "播放失败";
      });
  } catch (error) {
    console.error("处理 FLV 数据失败:", error);
    status.value = "处理失败";
  }
}

// 清理播放器
function cleanupPlayer() {
  if (player) {
    try {
      player.pause();
      player.unload();
      player.detachMediaElement();
      player.destroy();
    } catch (e) {
      console.error("清理播放器失败:", e);
    }
    player = null;
  }

  // 清空视频
  if (videoRef.value) {
    videoRef.value.src = "";
    videoRef.value.load();
  }
}

// 连接 WebSocket
function connectWebSocket() {
  console.log("正在连接 WebSocket:", WS_URL);
  status.value = "正在连接...";

  ws = new WebSocket(WS_URL);
  ws.binaryType = "arraybuffer";

  ws.onopen = () => {
    console.log("WebSocket 连接成功");
    isConnected.value = true;
    status.value = "已连接";
  };

  ws.onmessage = (event) => {
    try {
      if (typeof event.data === "string") {
        // 文本消息（可能是服务器状态信息）
        console.log("收到文本消息:", event.data);
      } else {
        // 二进制 FLV 数据
        console.log("收到二进制 FLV 数据:", event.data.byteLength, "字节");
        handleFlvData(event.data);
      }
    } catch (error) {
      console.error("处理消息失败:", error);
    }
  };

  ws.onerror = (error) => {
    console.error("WebSocket 错误:", error);
    isConnected.value = false;
    status.value = "连接错误";
  };

  ws.onclose = (event) => {
    console.log("WebSocket 关闭:", event.code, event.reason);
    isConnected.value = false;
    isStreaming.value = false;
    status.value = "连接关闭";

    // 3秒后自动重连
    setTimeout(() => {
      if (!isConnected.value) {
        connectWebSocket();
      }
    }, 3000);
  };
}

// 重新连接
function reconnect() {
  if (ws) {
    ws.close();
  }
  setTimeout(connectWebSocket, 1000);
}

// 生命周期
onMounted(() => {
  connectWebSocket();
});

onBeforeUnmount(() => {
  if (isStreaming.value) {
    stopStream();
  }
  cleanupPlayer();
  if (ws) {
    ws.close();
  }
});
</script>

<template>
  <div class="simple-flv-player">
    <div class="header">
      <h2>WebSocket FLV 流播放器</h2>
      <div class="status">
        状态: {{ status }}
        <span :class="['indicator', { online: isConnected }]">●</span>
      </div>
    </div>

    <div class="controls">
      <button @click="startStream" :disabled="!isConnected || isStreaming" class="btn start">开始推流</button>

      <button @click="stopStream" :disabled="!isConnected || !isStreaming" class="btn stop">停止推流</button>

      <button @click="reconnect" :disabled="isConnected" class="btn reconnect">重新连接</button>
    </div>

    <div class="video-wrapper">
      <video
        ref="videoRef"
        controls
        muted
        autoplay
        class="video"
        poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQ1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuetieW+heaOqOa1gTwvdGV4dD48L3N2Zz4="
      >
        您的浏览器不支持视频播放
      </video>
    </div>

    <div class="info">
      <h3>使用说明</h3>
      <ul>
        <li>
          确保后端服务运行在: <code>{{ WS_URL }}</code>
        </li>
        <li>点击"开始推流"发送开始指令</li>
        <li>后端推送 messageType: 50 的 FLV 数据</li>
        <li>前端自动解析并播放视频</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.simple-flv-player {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.header h2 {
  margin: 0;
  color: #333;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #666;
}

.indicator {
  font-size: 12px;
  color: #ff4d4f;
}

.indicator.online {
  color: #52c41a;
}

.controls {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.start {
  background: #52c41a;
  color: white;
}

.btn.start:hover:not(:disabled) {
  background: #73d13d;
}

.btn.stop {
  background: #ff4d4f;
  color: white;
}

.btn.stop:hover:not(:disabled) {
  background: #ff7875;
}

.btn.reconnect {
  background: #1890ff;
  color: white;
}

.btn.reconnect:hover:not(:disabled) {
  background: #40a9ff;
}

.video-wrapper {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.video {
  width: 100%;
  height: 400px;
  background: #000;
}

.info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info h3 {
  margin-top: 0;
  color: #333;
}

.info ul {
  margin: 0;
  padding-left: 20px;
}

.info li {
  margin-bottom: 8px;
  color: #555;
}

.info code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 13px;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .video {
    height: 250px;
  }
}
</style>
