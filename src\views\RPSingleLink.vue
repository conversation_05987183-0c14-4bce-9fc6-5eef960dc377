<template>
  <div ref="mapContainer" class="w-full h-full">
    <!-- 测距工具栏 -->
    <div class="absolute top-4 right-4 z-20 flex flex-col space-y-2 bg-white/90 p-3 rounded-lg shadow-lg">
      <div class="text-sm font-medium text-gray-700 mb-2">测距工具</div>

      <!-- 平面测距 -->
      <button
        :class="[
          'px-3 py-2 rounded text-sm font-medium transition-colors',
          measure?.isMeasuring ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-blue-500 text-white hover:bg-blue-600',
        ]"
        @click="measure?.toggleMeasure"
      >
        📏 {{ measure?.isMeasuring ? "结束平面测距" : "开始平面测距" }}
      </button>

      <!-- 空间测距 -->
      <button
        :class="[
          'px-3 py-2 rounded text-sm font-medium transition-colors',
          measure?.isSpaceMeasuring
            ? 'bg-red-500 text-white hover:bg-red-600'
            : 'bg-purple-500 text-white hover:bg-purple-600',
        ]"
        @click="measure?.toggleSpaceMeasure"
      >
        🚀 {{ measure?.isSpaceMeasuring ? "取消空间测距" : "开始空间测距" }}
      </button>

      <!-- 清除所有测距 -->
      <button
        class="px-3 py-2 rounded text-sm font-medium bg-orange-500 text-white hover:bg-orange-600 transition-colors"
        @click="measure?.clearMeasure"
      >
        🗑️ 清除所有测距
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as Cesium from "cesium";
import { EllipsoidTerrainProvider, UrlTemplateImageryProvider, Cartesian3 } from "cesium";
import { commCapData } from "@/utils/radarPower.js";
import { useMeasure } from "@/hooks/useMeasure.js";

// —— Cesium 初始化 ——
const mapContainer = ref(null);
let viewer;
let measure;
function initMap() {
  viewer = new Cesium.Viewer(mapContainer.value, {
    terrainProvider: new EllipsoidTerrainProvider(),
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: true,
    fullscreenElement: mapContainer.value,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "https://webrd0{s}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}",
      subdomains: ["1", "2", "3", "4"],
      maximumLevel: 18,
    }),
  });
  viewer._cesiumWidget._creditContainer.style.display = "none";
  viewer.camera.setView({
    destination: Cartesian3.fromDegrees((124.8886 + 131.1114) / 2, (33.482 + 38.518) / 2, 500000),
    orientation: { heading: 0, pitch: -Math.PI / 2, roll: 0 },
  });
  const layers = viewer.imageryLayers;
  for (let i = layers.length - 1; i >= 0; i--) layers.remove(layers.get(i));
  // 基础卫星影像图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
    })
  );

  // 叠加路网和标注图层
  layers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: "https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      maximumLevel: 18,
      minimumLevel: 3,
      alpha: 0.8, // 设置透明度以便更好地查看叠加效果
    })
  );

  // **在这里** 调用 useMeasure，把 viewer 传进去
  measure = useMeasure(viewer, mapContainer);

  // 添加示例实体用于空间测距演示
  addDemoEntities();
}

/**
 * 把 pEr 网格渲染到一个小画布上，然后再拉伸到大尺寸以开启平滑插值
 * @param {number[][]} pEr    原始功率矩阵，大小 nRow×nCol
 * @param {number}    nRow
 * @param {number}    nCol
 * @param {number}    smoothSize 最终画布边长（px），建议512或1024
 */
function makeHeatCanvas(pEr, nRow, nCol, smoothSize = 512) {
  // 1) 先在一个 nCol×nRow 的小画布上，逐像素填色
  const small = document.createElement("canvas");
  small.width = nCol;
  small.height = nRow;
  const sCtx = small.getContext("2d");

  // 归一化找 min/max
  const flat = pEr.flat();
  const vMin = Math.min(...flat);
  const vMax = Math.max(...flat);

  for (let i = 0; i < nRow; i++) {
    for (let j = 0; j < nCol; j++) {
      const t = (pEr[i][j] - vMin) / (vMax - vMin);
      const hue = (1 - t) * 0.66; // HSL 0.66→0 (蓝→红)
      const col = Cesium.Color.fromHsl(hue, 1, 0.5).withAlpha(0.6).toCssColorString();
      sCtx.fillStyle = col;
      sCtx.fillRect(j, i, 1, 1);
    }
  }

  // 2) 把小画布“平滑拉伸”到 smoothSize×smoothSize
  const big = document.createElement("canvas");
  big.width = smoothSize;
  big.height = smoothSize;
  const bCtx = big.getContext("2d");
  bCtx.imageSmoothingEnabled = true;
  bCtx.imageSmoothingQuality = "high";
  // 一次性插值到大尺寸
  bCtx.drawImage(small, 0, 0, smoothSize, smoothSize);

  return big;
}

/**
 * 添加演示实体（飞机和塔台）用于空间测距
 */
function addDemoEntities() {
  // 添加塔台
  viewer.entities.add({
    name: "控制塔台",
    position: Cesium.Cartesian3.fromDegrees(119.0, 36.0, 100), // 地面高度100米
    billboard: {
      image:
        "data:image/svg+xml;base64," +
        btoa(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <rect x="14" y="20" width="4" height="8" fill="#666"/>
          <rect x="12" y="16" width="8" height="4" fill="#888"/>
          <rect x="10" y="12" width="12" height="4" fill="#aaa"/>
          <rect x="8" y="8" width="16" height="4" fill="#ccc"/>
          <circle cx="16" cy="6" r="2" fill="#f00"/>
        </svg>
      `),
      scale: 1.5,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
    label: {
      text: "🗼 控制塔台",
      font: "14px sans-serif",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -40),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
  });

  // 添加飞机1（低空）
  viewer.entities.add({
    name: "客机A320",
    position: Cesium.Cartesian3.fromDegrees(119.8, 35.8, 3000), // 3000米高度
    billboard: {
      image:
        "data:image/svg+xml;base64," +
        btoa(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="16" cy="16" rx="14" ry="4" fill="#0066cc"/>
          <ellipse cx="16" cy="16" rx="10" ry="2" fill="#0088ff"/>
          <rect x="14" y="8" width="4" height="16" fill="#0066cc"/>
          <rect x="6" y="14" width="20" height="4" fill="#0066cc"/>
        </svg>
      `),
      scale: 1.2,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
    },
    label: {
      text: "✈️ 客机A320 (3km)",
      font: "12px sans-serif",
      fillColor: Cesium.Color.CYAN,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -30),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
  });

  // 添加飞机2（高空）
  viewer.entities.add({
    name: "战斗机F16",
    position: Cesium.Cartesian3.fromDegrees(119.2, 36.2, 8000), // 8000米高度
    billboard: {
      image:
        "data:image/svg+xml;base64," +
        btoa(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <polygon points="16,4 20,12 28,14 20,16 16,28 12,16 4,14 12,12" fill="#ff6600"/>
          <polygon points="16,6 18,12 24,13 18,14 16,24 14,14 8,13 14,12" fill="#ff8800"/>
        </svg>
      `),
      scale: 1.0,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
    },
    label: {
      text: "🚁 战斗机F16 (8km)",
      font: "12px sans-serif",
      fillColor: Cesium.Color.ORANGE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -30),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
  });

  // 添加直升机（中等高度）
  viewer.entities.add({
    name: "救援直升机",
    position: Cesium.Cartesian3.fromDegrees(119.9, 36.1, 1500), // 1500米高度
    billboard: {
      image:
        "data:image/svg+xml;base64," +
        btoa(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="16" cy="18" rx="12" ry="6" fill="#00aa00"/>
          <rect x="14" y="12" width="4" height="12" fill="#00aa00"/>
          <rect x="4" y="16" width="24" height="2" fill="#006600"/>
          <rect x="14" y="8" width="4" height="4" fill="#006600"/>
        </svg>
      `),
      scale: 1.1,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
    },
    label: {
      text: "🚁 救援直升机 (1.5km)",
      font: "12px sans-serif",
      fillColor: Cesium.Color.LIME,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -30),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
  });
}

/**
 * 用 CanvasMaterialProperty 在地图上画一个圆形的贴图热力图
 * @param {*} opts
 * @param {number} opts.lon      圆心经度
 * @param {number} opts.lat      圆心纬度
 * @param {number} opts.radius   圆半径（米）
 * @param {number[][]} opts.pEr  功率矩阵
 * @param {number}    opts.nRow
 * @param {number}    opts.nCol
 */
function drawSmoothHeatCircle({ lon, lat, radius, pEr, nRow, nCol }) {
  // 生成平滑后的 Canvas
  const canvas = makeHeatCanvas(pEr, nRow, nCol, 512);

  // 用它做 ImageMaterial
  const material = new Cesium.ImageMaterialProperty({
    image: canvas,
    transparent: true,
  });

  // 添加到 Cesium
  viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(lon, lat),
    ellipse: {
      semiMajorAxis: radius,
      semiMinorAxis: radius,
      material: material,
      height: 0,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      outline: false,
    },
  });
}

// —— 示范：假设你已经通过 fetch 拿到上面那个 JSON ——
onMounted(async () => {
  initMap();

  // 举例：用本地变量 powerData，实际上请改为你的 fetch 调用
  const powerData = commCapData;
  // 假设你已 fetch 到后端数据为 json.data
  const { nRow, nCol, lonlim, latlim, pEr } = powerData.data;

  // 计算圆心和半径
  const centerLon = (lonlim[0] + lonlim[1]) / 2;
  const centerLat = (latlim[0] + latlim[1]) / 2;
  const c0 = Cesium.Cartographic.fromDegrees(centerLon, centerLat); // 圆心经纬坐标转笛卡尔坐标
  const ce = Cesium.Cartographic.fromDegrees(lonlim[1], centerLat); // 圆心到东边界的笛卡尔距离
  const radius = new Cesium.EllipsoidGeodesic(c0, ce).surfaceDistance; //计算半径

  // drawSmoothHeatCircle({
  //   lon: centerLon,
  //   lat: centerLat,
  //   radius,
  //   pEr,
  //   nRow,
  //   nCol,
  // });
});

onBeforeUnmount(() => {
  viewer && viewer.destroy();
});
</script>

<style scoped>
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
