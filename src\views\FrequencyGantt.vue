<template>
  <div class="frequency-gantt-container">
    <div class="toolbar">
      <div class="zoom-controls">
        <span>缩放级别：</span>
        <el-button-group>
          <el-button :type="zoomLevel === 10 ? 'primary' : 'default'" size="small" @click="setZoomLevel(10)">
            10MHz
          </el-button>
          <el-button :type="zoomLevel === 50 ? 'primary' : 'default'" size="small" @click="setZoomLevel(50)">
            50MHz
          </el-button>
          <el-button :type="zoomLevel === 100 ? 'primary' : 'default'" size="small" @click="setZoomLevel(100)">
            100MHz
          </el-button>
        </el-button-group>
      </div>
      <el-button type="success" size="small" @click="exportToPDF">导出PDF</el-button>

      <!-- 调试信息 -->
      <div class="debug-info">
        <small>
          范围: {{ minFreq }}-{{ maxFreq }}MHz | 缩放: {{ zoomLevel }}MHz | 像素比: {{ pixelsPerMHz.toFixed(2) }}px/MHz
          | 图表宽度: {{ chartWidth }}px
        </small>
      </div>
    </div>

    <div class="gantt-chart" ref="chartContainer">
      <!-- 频率坐标轴 -->
      <div class="frequency-axis">
        <div class="axis-labels">
          <div
            v-for="tick in frequencyTicks"
            :key="tick.freq"
            class="axis-tick"
            :style="{ left: tick.position + 'px' }"
          >
            <div class="tick-line"></div>
            <div class="tick-label">{{ tick.label }}</div>
          </div>
        </div>
      </div>

      <!-- 甘特图主体 -->
      <div class="gantt-body">
        <div class="row-labels">
          <div v-for="link in linkData" :key="link.id" class="row-label">
            {{ link.name }}
          </div>
        </div>

        <div class="chart-area">
          <div v-for="link in linkData" :key="link.id" class="chart-row">
            <!-- 占用频段 -->
            <div
              v-for="segment in link.segments"
              :key="segment.id"
              class="frequency-segment"
              :class="[segment.type, { conflict: segment.conflict }]"
              :style="getSegmentStyle(segment)"
              :title="getSegmentTooltip(segment)"
            >
              <span class="segment-label">{{ segment.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 频率范围配置
      minFreq: 1200, // MHz
      maxFreq: 1600, // MHz
      zoomLevel: 10, // MHz per grid

      // 链路数据
      linkData: [
        {
          id: 1,
          name: "F1 A1-A2 下行",
          segments: [
            { id: "1-1", type: "occupied", start: 1325, end: 1325, label: "1.325GHz", conflict: false },
            { id: "1-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 2,
          name: "F2 B1-B2 下行",
          segments: [
            { id: "2-1", type: "occupied", start: 1415, end: 1415, label: "1.415GHz", conflict: false },
            { id: "2-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 3,
          name: "F1 C1-C2 下行",
          segments: [
            { id: "3-1", type: "occupied", start: 1385, end: 1385, label: "1.385GHz", conflict: false },
            { id: "3-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 4,
          name: "F2 D1-D2 下行",
          segments: [
            { id: "4-1", type: "occupied", start: 1365, end: 1365, label: "1.365GHz", conflict: false },
            { id: "4-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 5,
          name: "F1 A2-A1 上行",
          segments: [
            { id: "5-1", type: "occupied", start: 1325, end: 1325, label: "1.325GHz", conflict: false }, // 故意设置冲突
            { id: "5-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 6,
          name: "F2 B2-B1 上行",
          segments: [
            { id: "6-1", type: "occupied", start: 1455, end: 1455, label: "1.455GHz", conflict: false },
            { id: "6-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 7,
          name: "F1 C2-C1 上行",
          segments: [
            { id: "7-1", type: "occupied", start: 1445, end: 1445, label: "1.445GHz", conflict: false },
            { id: "7-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 8,
          name: "F2 D2-D1 上行",
          segments: [
            { id: "8-1", type: "occupied", start: 1495, end: 1495, label: "1.495GHz", conflict: false },
            { id: "8-2", type: "idle", start: 1300, end: 1310, label: "空闲", conflict: false },
          ],
        },
        {
          id: 9,
          name: "链路安全备用",
          segments: [{ id: "9-1", type: "backup", start: 1465, end: 1505, label: "备用频段", conflict: false }],
        },
      ],
    };
  },
  mounted() {
    this.detectConflicts();
    this.updateGridWidth();
  },

  watch: {
    zoomLevel() {
      this.updateGridWidth();
    },
  },
  computed: {
    // 计算图表区域宽度
    chartWidth() {
      return 1000; // 固定图表宽度
    },

    // 计算频率范围和像素比例
    freqRange() {
      return this.maxFreq - this.minFreq;
    },

    pixelsPerMHz() {
      return this.chartWidth / this.freqRange;
    },

    // 计算频率刻度
    frequencyTicks() {
      const ticks = [];

      for (let freq = this.minFreq; freq <= this.maxFreq; freq += this.zoomLevel) {
        const position = (freq - this.minFreq) * this.pixelsPerMHz;
        ticks.push({
          freq,
          position,
          label: freq >= 1000 ? `${(freq / 1000).toFixed(1)}G` : `${freq}M`,
        });
      }
      return ticks;
    },
  },
  methods: {
    // 设置缩放级别
    setZoomLevel(level) {
      this.zoomLevel = level;
    },

    // 检测频率冲突
    detectConflicts() {
      const occupiedFreqs = {};

      // 收集所有占用频率
      this.linkData.forEach((link) => {
        link.segments.forEach((segment) => {
          if (segment.type === "occupied") {
            const freq = segment.start;
            if (!occupiedFreqs[freq]) {
              occupiedFreqs[freq] = [];
            }
            occupiedFreqs[freq].push(segment);
          }
        });
      });

      // 标记冲突
      Object.keys(occupiedFreqs).forEach((freq) => {
        if (occupiedFreqs[freq].length > 1) {
          occupiedFreqs[freq].forEach((segment) => {
            segment.conflict = true;
          });
        }
      });
    },

    // 计算频段样式
    getSegmentStyle(segment) {
      const left = (segment.start - this.minFreq) * this.pixelsPerMHz;
      let width;

      if (segment.type === "occupied") {
        // 占用频率显示为点（固定宽度）
        width = 8;
      } else {
        // 频段显示为条形
        width = (segment.end - segment.start) * this.pixelsPerMHz;
      }

      return {
        left: left + "px",
        width: width + "px",
      };
    },

    // 获取频段提示信息
    getSegmentTooltip(segment) {
      if (segment.type === "occupied") {
        return `占用频率: ${segment.start}MHz${segment.conflict ? " (冲突)" : ""}`;
      } else if (segment.type === "idle") {
        return `空闲频段: ${segment.start}-${segment.end}MHz`;
      } else if (segment.type === "backup") {
        return `备用频段: ${segment.start}-${segment.end}MHz`;
      }
      return "";
    },

    // 更新网格宽度
    updateGridWidth() {
      this.$nextTick(() => {
        const gridWidth = this.zoomLevel * this.pixelsPerMHz;
        const chartArea = this.$refs.chartContainer?.querySelector(".chart-area");
        if (chartArea) {
          chartArea.style.setProperty("--grid-width", `${gridWidth}px`);
        }
      });
    },

    // 导出PDF
    exportToPDF() {
      // 这里可以使用html2canvas + jsPDF实现
      console.log("导出PDF功能待实现");
    },
  },
};
</script>

<style scoped>
.frequency-gantt-container {
  width: 100%;
  height: 100vh;
  background: #f5f7fa;
}

.toolbar {
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.zoom-controls span {
  font-weight: 500;
  color: #606266;
}

.debug-info {
  color: #909399;
  font-size: 12px;
}

.gantt-chart {
  padding: 20px;
  height: calc(100vh - 80px);
  overflow: auto;
}

/* 频率坐标轴 */
.frequency-axis {
  height: 60px;
  background: white;
  border: 1px solid #e4e7ed;
  border-bottom: 2px solid #409eff;
  position: relative;
  margin-bottom: 10px;
  margin-left: 200px; /* 与row-labels宽度对齐 */
}

.axis-labels {
  position: relative;
  height: 100%;
  width: 1000px; /* 固定宽度与chartWidth一致 */
}

.axis-tick {
  position: absolute;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-left: 1px solid #ddd;
}

.tick-line {
  width: 2px;
  height: 20px;
  background: #666;
  margin-bottom: 5px;
}

.tick-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 2px;
}

/* 甘特图主体 */
.gantt-body {
  display: flex;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.row-labels {
  width: 200px;
  background: #fafafa;
  border-right: 1px solid #e4e7ed;
}

.row-label {
  height: 50px;
  padding: 15px 10px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.chart-area {
  width: 1000px; /* 固定宽度与chartWidth一致 */
  position: relative;
  overflow-x: auto;
  --grid-width: 25px; /* 默认网格宽度，会通过JS动态更新 */
}

.chart-row {
  height: 50px;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
  background-image: repeating-linear-gradient(
    to right,
    transparent,
    transparent calc(var(--grid-width) - 1px),
    #f0f0f0 calc(var(--grid-width) - 1px),
    #f0f0f0 var(--grid-width)
  );
}

/* 频段样式 */
.frequency-segment {
  position: absolute;
  top: 10px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.frequency-segment:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.segment-label {
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 占用频率（点状） */
.frequency-segment.occupied {
  background: #409eff;
  border: 2px solid #337ecc;
  border-radius: 50%;
  width: 12px !important;
  height: 12px !important;
  top: 19px;
  margin-left: -6px; /* 居中对齐到刻度线 */
}

.frequency-segment.occupied .segment-label {
  display: none;
}

/* 空闲频段 */
.frequency-segment.idle {
  background: #67c23a;
  border: 1px solid #5daf34;
}

/* 备用频段 */
.frequency-segment.backup {
  background: #e6a23c;
  border: 1px solid #cf9236;
}

/* 冲突频率 */
.frequency-segment.conflict {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .row-labels {
    width: 150px;
  }

  .row-label {
    font-size: 12px;
    padding: 10px 5px;
  }
}
</style>
