<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

// 响应式状态
const videoRef = ref(null);
const isConnected = ref(false);
const isStreaming = ref(false);
const status = ref("未连接");

// WebSocket 和播放器实例
let ws = null;
let player = null;
let flvBuffer = [];
let isPlayerReady = false;

// 配置
const WS_URL = "ws://***********:9085/ws/video-stream";

// 开始推流
function startStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接，请等待连接建立");
    return;
  }

  // 发送开始指令
  const command = {
    action: "start",
    streamUrl: "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8",
  };

  ws.send(JSON.stringify(command));
  console.log("发送开始指令:", command);

  // 清空缓冲区，准备接收新数据
  flvBuffer = [];
  isPlayerReady = false;

  isStreaming.value = true;
  status.value = "正在请求流...";
}

// 停止推流
function stopStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    alert("WebSocket 未连接");
    return;
  }

  // 发送停止指令
  const command = { action: "stop" };
  ws.send(JSON.stringify(command));
  console.log("发送停止指令:", command);

  // 清理播放器和缓冲区
  cleanupPlayer();
  flvBuffer = [];
  isPlayerReady = false;

  isStreaming.value = false;
  status.value = "已停止";
}

// 处理二进制 FLV 数据
function handleBinaryFlvData(data) {
  console.log("收到二进制 FLV 数据:", data.byteLength, "字节");

  try {
    // 将数据添加到缓冲区
    flvBuffer.push(new Uint8Array(data));

    // 如果播放器还未准备好，尝试初始化
    if (!isPlayerReady && flvBuffer.length > 0) {
      initializePlayer();
    } else if (isPlayerReady && player) {
      // 如果播放器已准备好，可以考虑动态更新数据
      // 这里暂时重新创建播放器来处理新数据
      updatePlayerWithNewData();
    }
  } catch (error) {
    console.error("处理二进制数据失败:", error);
    status.value = "数据处理失败";
  }
}

// 初始化播放器
function initializePlayer() {
  if (!flvjs.isSupported()) {
    console.error("浏览器不支持 flv.js");
    status.value = "浏览器不支持";
    return;
  }

  try {
    // 合并所有缓冲的数据
    const totalLength = flvBuffer.reduce((sum, arr) => sum + arr.length, 0);
    console.log("合并数据总长度:", totalLength, "字节");

    if (totalLength === 0) {
      console.log("没有数据可播放");
      return;
    }

    const mergedData = new Uint8Array(totalLength);
    let offset = 0;

    for (const chunk of flvBuffer) {
      mergedData.set(chunk, offset);
      offset += chunk.length;
    }

    // 创建 Blob URL
    const blob = new Blob([mergedData], { type: "video/x-flv" });
    const url = URL.createObjectURL(blob);

    console.log("创建播放器，数据大小:", mergedData.length);

    // 清理旧播放器
    if (player) {
      cleanupPlayer();
    }

    // 创建新播放器
    player = flvjs.createPlayer({
      type: "flv",
      url: url,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      enableWorker: false,
      enableStashBuffer: true,
      stashInitialSize: 128,
      autoCleanupSourceBuffer: true,
    });

    // 绑定到视频元素
    player.attachMediaElement(videoRef.value);

    // 监听播放器事件
    player.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      console.error("播放器错误:", errorType, errorDetail);
      console.error("错误详情:", errorInfo);
      status.value = "播放错误";
      isPlayerReady = false;
    });

    player.on(flvjs.Events.LOADING_COMPLETE, () => {
      console.log("FLV 数据加载完成");
    });

    player.on(flvjs.Events.MEDIA_INFO, (mediaInfo) => {
      console.log("媒体信息:", mediaInfo);
    });

    player.on(flvjs.Events.STATISTICS_INFO, (statisticsInfo) => {
      console.log("统计信息:", statisticsInfo);
    });

    // 加载并播放
    player.load();

    player
      .play()
      .then(() => {
        console.log("播放器启动成功");
        status.value = "正在播放";
        isPlayerReady = true;
      })
      .catch((err) => {
        console.error("播放启动失败:", err);
        status.value = "播放失败";
        isPlayerReady = false;
      });
  } catch (error) {
    console.error("初始化播放器失败:", error);
    status.value = "初始化失败";
    isPlayerReady = false;
  }
}

// 用新数据更新播放器
function updatePlayerWithNewData() {
  // 对于连续的流数据，重新初始化播放器
  // 这是一个简单的方法，更复杂的实现可能需要使用 MediaSource API
  console.log("更新播放器数据");
  isPlayerReady = false;
  initializePlayer();
}

// 清理播放器
function cleanupPlayer() {
  if (player) {
    try {
      player.pause();
      player.unload();
      player.detachMediaElement();
      player.destroy();
      console.log("播放器已清理");
    } catch (e) {
      console.error("清理播放器失败:", e);
    }
    player = null;
  }

  if (videoRef.value) {
    videoRef.value.src = "";
    videoRef.value.load();
  }

  isPlayerReady = false;
}

// 连接 WebSocket
function connectWebSocket() {
  console.log("正在连接 WebSocket:", WS_URL);
  status.value = "正在连接...";

  ws = new WebSocket(WS_URL);
  ws.binaryType = "arraybuffer";

  ws.onopen = () => {
    console.log("WebSocket 连接成功");
    isConnected.value = true;
    status.value = "已连接";
  };

  ws.onmessage = (event) => {
    if (typeof event.data === "string") {
      // 文本消息（服务器状态或确认信息）
      console.log("收到文本消息:", event.data);
    } else {
      // 二进制 FLV 数据
      handleBinaryFlvData(event.data);
    }
  };

  ws.onerror = (error) => {
    console.error("WebSocket 错误:", error);
    isConnected.value = false;
    status.value = "连接错误";
  };

  ws.onclose = (event) => {
    console.log("WebSocket 关闭:", event.code, event.reason);
    isConnected.value = false;
    isStreaming.value = false;
    status.value = "连接关闭";

    // 3秒后自动重连
    setTimeout(() => {
      if (!isConnected.value) {
        console.log("尝试自动重连...");
        connectWebSocket();
      }
    }, 3000);
  };
}

// 重新连接
function reconnect() {
  if (ws) {
    ws.close();
  }
  setTimeout(connectWebSocket, 1000);
}

// 清空缓冲区
function clearBuffer() {
  flvBuffer = [];
  console.log("缓冲区已清空");
}

// 生命周期
onMounted(() => {
  connectWebSocket();
});

onBeforeUnmount(() => {
  if (isStreaming.value) {
    stopStream();
  }
  cleanupPlayer();
  if (ws) {
    ws.close();
  }
});
</script>

<template>
  <div class="binary-flv-player">
    <div class="header">
      <h2>二进制 FLV 流播放器</h2>
      <div class="status">
        状态: {{ status }}
        <span :class="['indicator', { online: isConnected }]">●</span>
      </div>
    </div>

    <div class="controls">
      <button @click="startStream" :disabled="!isConnected || isStreaming" class="btn start">开始推流</button>

      <button @click="stopStream" :disabled="!isConnected || !isStreaming" class="btn stop">停止推流</button>

      <button @click="reconnect" :disabled="isConnected" class="btn reconnect">重新连接</button>

      <button @click="clearBuffer" class="btn clear">清空缓冲</button>
    </div>

    <div class="video-wrapper">
      <video ref="videoRef" controls muted autoplay class="video">您的浏览器不支持视频播放</video>
    </div>

    <div class="info">
      <h3>二进制流处理说明</h3>
      <ul>
        <li>后端直接推送二进制 FLV 数据</li>
        <li>前端自动缓冲并合并数据块</li>
        <li>使用 flv.js 解析并播放视频</li>
        <li>支持实时流和点播内容</li>
      </ul>

      <div class="stats">
        <p><strong>WebSocket:</strong> {{ WS_URL }}</p>
        <p><strong>缓冲块数:</strong> {{ flvBuffer.length }}</p>
        <p><strong>播放器状态:</strong> {{ isPlayerReady ? "就绪" : "未就绪" }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.binary-flv-player {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.header h2 {
  margin: 0;
  color: #333;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #666;
}

.indicator {
  font-size: 12px;
  color: #ff4d4f;
}

.indicator.online {
  color: #52c41a;
}

.controls {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.start {
  background: #52c41a;
  color: white;
}

.btn.start:hover:not(:disabled) {
  background: #73d13d;
}

.btn.stop {
  background: #ff4d4f;
  color: white;
}

.btn.stop:hover:not(:disabled) {
  background: #ff7875;
}

.btn.reconnect {
  background: #1890ff;
  color: white;
}

.btn.reconnect:hover:not(:disabled) {
  background: #40a9ff;
}

.btn.clear {
  background: #faad14;
  color: white;
}

.btn.clear:hover {
  background: #ffc53d;
}

.video-wrapper {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.video {
  width: 100%;
  height: 400px;
  background: #000;
}

.info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info h3 {
  margin-top: 0;
  color: #333;
}

.info ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.info li {
  margin-bottom: 8px;
  color: #555;
}

.stats {
  background: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.stats p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .video {
    height: 250px;
  }
}
</style>
