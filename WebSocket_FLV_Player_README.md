# WebSocket FLV 流播放器使用说明

## 概述

这是一个基于 Vue 3 和 flv.js 的 WebSocket FLV 流播放器，用于接收后端通过 WebSocket 推送的 FLV 视频流数据并进行播放。

## 功能特性

- ✅ WebSocket 连接管理
- ✅ 发送开始/停止推流指令
- ✅ 接收并播放 FLV 格式的视频流
- ✅ 支持 messageType: 50 的数据格式
- ✅ 实时日志显示
- ✅ 自动重连机制
- ✅ 响应式设计

## 技术栈

- **Vue 3** - 前端框架
- **flv.js** - FLV 视频播放库
- **WebSocket** - 实时通信
- **MediaSource API** - 媒体流处理（备选方案）

## 文件说明

### 1. VideoStreamPlayer.vue
基础版本的视频流播放器，包含基本的 WebSocket 连接和 FLV 播放功能。

### 2. WebSocketFlvPlayer.vue
完整版本的播放器，包含：
- 详细的日志记录
- 多种数据格式支持
- 错误处理和重连机制
- 更好的用户界面

### 3. SkyWaveSingleLinkNew.vue
简化版本，直接引用 VideoStreamPlayer 组件。

## 使用方法

### 1. 基本使用

```vue
<template>
  <div>
    <WebSocketFlvPlayer />
  </div>
</template>

<script setup>
import WebSocketFlvPlayer from './views/WebSocketFlvPlayer.vue';
</script>
```

### 2. 配置说明

在 `WebSocketFlvPlayer.vue` 中修改以下配置：

```javascript
// WebSocket 服务器地址
const wsUrl = "ws://***********:9085/ws/video-stream";

// 开始推流指令
const startCommand = {
  action: "start",
  streamUrl: "your-rtsp-url-here"
};

// 停止推流指令
const stopCommand = { action: "stop" };
```

## 数据格式支持

### 1. JSON 消息格式
```json
{
  "messageType": 50,
  "data": "base64编码的FLV数据或二进制数组"
}
```

### 2. 二进制数据
直接发送 FLV 格式的二进制数据。

## 工作流程

1. **建立连接**: 页面加载时自动连接到 WebSocket 服务器
2. **发送指令**: 点击"开始推流"按钮发送开始指令
3. **接收数据**: 监听 WebSocket 消息，识别 FLV 数据
4. **播放视频**: 使用 flv.js 解析并播放 FLV 数据
5. **停止播放**: 点击"停止推流"按钮停止播放并清理资源

## 关键代码说明

### WebSocket 连接
```javascript
ws = new WebSocket(wsUrl);
ws.binaryType = 'arraybuffer'; // 设置接收二进制数据
```

### FLV 数据处理
```javascript
function handleFlvData(data) {
  // 创建 Blob URL
  const blob = new Blob([data], { type: 'video/x-flv' });
  const url = URL.createObjectURL(blob);
  
  // 创建 flv.js 播放器
  player = flvjs.createPlayer({
    type: 'flv',
    url: url,
    isLive: true,
    hasAudio: true,
    hasVideo: true,
  });
  
  player.attachMediaElement(videoRef.value);
  player.load();
  player.play();
}
```

### 消息类型识别
```javascript
ws.onmessage = (event) => {
  if (typeof event.data === 'string') {
    const message = JSON.parse(event.data);
    if (message.messageType === 50) {
      // 处理 FLV 数据
      handleFlvData(message.data);
    }
  } else {
    // 直接处理二进制 FLV 数据
    handleFlvData(event.data);
  }
};
```

## 故障排除

### 1. 连接问题
- 检查 WebSocket 服务器地址是否正确
- 确认服务器是否正在运行
- 检查网络连接和防火墙设置

### 2. 播放问题
- 确认浏览器支持 flv.js
- 检查 FLV 数据格式是否正确
- 查看控制台错误信息

### 3. 数据格式问题
- 确认后端发送的数据格式
- 检查 messageType 是否为 50
- 验证 base64 编码是否正确

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## 注意事项

1. **HTTPS 环境**: 在 HTTPS 页面中需要使用 WSS 协议
2. **跨域问题**: 确保 WebSocket 服务器允许跨域连接
3. **内存管理**: 长时间播放需要注意内存使用情况
4. **错误处理**: 建议添加完善的错误处理机制

## 扩展功能

### 1. 录制功能
可以扩展添加视频录制功能：
```javascript
// 使用 MediaRecorder API 录制视频
const mediaRecorder = new MediaRecorder(videoElement.captureStream());
```

### 2. 截图功能
```javascript
// 截取视频帧
function captureFrame() {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.drawImage(videoElement, 0, 0);
  return canvas.toDataURL('image/png');
}
```

### 3. 全屏播放
```javascript
// 全屏播放
function enterFullscreen() {
  if (videoElement.requestFullscreen) {
    videoElement.requestFullscreen();
  }
}
```

## 性能优化建议

1. **缓冲区管理**: 合理设置 flv.js 的缓冲区大小
2. **内存清理**: 及时清理不用的播放器实例
3. **连接复用**: 避免频繁创建和销毁 WebSocket 连接
4. **错误重试**: 实现智能的重连机制

## 更新日志

- v1.0.0: 基础功能实现
- v1.1.0: 添加详细日志和错误处理
- v1.2.0: 支持多种数据格式
- v1.3.0: 优化用户界面和响应式设计
