<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import flvjs from "flv.js";

/** 1️⃣ 画面元素的 ref */
const videoRef = ref(null);

/** 2️⃣ WebSocket地址和状态 */
const wsUrl = "ws://***********:9085/ws/video-stream";
const isConnected = ref(false);
const isStreaming = ref(false);
const connectionStatus = ref("未连接");

/** 3️⃣ 存放 WebSocket 和 flv.js 播放器实例 */
let ws = null;
let player = null;
let flvDataBuffer = [];

/** 4️⃣ 开始推流 */
function startStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    console.error("WebSocket 尚未连接");
    return;
  }

  // 发送开始指令
  const startCommand = {
    action: "start",
    streamUrl: "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear2/prog_index.m3u8"
  };
  
  ws.send(JSON.stringify(startCommand));
  console.log("发送开始指令:", startCommand);
  
  isStreaming.value = true;
  connectionStatus.value = "正在请求流...";
}

/** 5️⃣ 停止推流 */
function stopStream() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    console.error("WebSocket 尚未连接");
    return;
  }

  // 发送停止指令
  const stopCommand = { action: "stop" };
  ws.send(JSON.stringify(stopCommand));
  console.log("发送停止指令:", stopCommand);
  
  // 停止播放器
  destroyPlayer();
  isStreaming.value = false;
  connectionStatus.value = "已停止";
}

/** 6️⃣ 初始化播放器 */
function initPlayer() {
  if (!flvjs.isSupported()) {
    console.error("浏览器不支持 flv.js");
    return;
  }

  // 如果已有播放器，先销毁
  if (player) {
    destroyPlayer();
  }

  try {
    // 创建一个虚拟的 FLV 流 URL
    // 这里我们需要创建一个自定义的数据源来处理 WebSocket 接收的数据
    player = flvjs.createPlayer({
      type: 'flv',
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      url: 'ws://fake-url', // 占位符，实际数据通过 WebSocket 接收
    });

    player.attachMediaElement(videoRef.value);
    
    // 监听播放器事件
    player.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      console.error('FLV播放器错误:', errorType, errorDetail, errorInfo);
    });

    player.on(flvjs.Events.LOADING_COMPLETE, () => {
      console.log('FLV加载完成');
    });

    player.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {
      console.log('FLV流恢复');
    });

    console.log("播放器初始化完成");
    
  } catch (error) {
    console.error("初始化播放器失败:", error);
  }
}

/** 7️⃣ 销毁播放器 */
function destroyPlayer() {
  if (player) {
    try {
      player.pause();
      player.unload();
      player.detachMediaElement();
      player.destroy();
    } catch (e) {
      console.error("销毁播放器时出错:", e);
    }
    player = null;
  }
  
  // 清空视频元素
  if (videoRef.value) {
    videoRef.value.src = '';
    videoRef.value.load();
  }
  
  // 清空缓冲区
  flvDataBuffer = [];
}

/** 8️⃣ 处理接收到的 FLV 数据 */
function handleFlvData(flvData) {
  try {
    console.log("收到FLV数据，长度:", flvData.length);
    
    // 将数据添加到缓冲区
    flvDataBuffer.push(flvData);
    
    // 如果还没有播放器，先初始化
    if (!player) {
      // 对于通过 WebSocket 接收的 FLV 数据，我们需要使用不同的方法
      // 创建一个 Blob URL 来播放
      const blob = new Blob(flvDataBuffer, { type: 'video/x-flv' });
      const url = URL.createObjectURL(blob);
      
      if (flvjs.isSupported()) {
        player = flvjs.createPlayer({
          type: 'flv',
          url: url,
          isLive: true,
          hasAudio: true,
          hasVideo: true,
        });
        
        player.attachMediaElement(videoRef.value);
        player.load();
        
        player.play().then(() => {
          console.log("开始播放FLV流");
          connectionStatus.value = "正在播放";
        }).catch(err => {
          console.error("播放失败:", err);
          connectionStatus.value = "播放失败";
        });
      }
    }
    
  } catch (error) {
    console.error("处理 FLV 数据时出错:", error);
  }
}

/** 9️⃣ 连接WebSocket */
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    return;
  }

  connectionStatus.value = "正在连接...";
  ws = new WebSocket(wsUrl);
  
  // 设置二进制数据类型
  ws.binaryType = 'arraybuffer';
  
  ws.onopen = () => {
    console.log("WebSocket 已连接");
    isConnected.value = true;
    connectionStatus.value = "已连接";
  };
  
  ws.onmessage = (event) => {
    try {
      // 检查是否是文本消息
      if (typeof event.data === 'string') {
        const message = JSON.parse(event.data);
        console.log("收到文本消息:", message);
        
        // 检查是否是包含 FLV 数据的消息
        if (message.messageType === 50) {
          console.log("收到 FLV 数据消息");
          
          // 处理 FLV 数据
          if (message.data) {
            let flvData;
            if (typeof message.data === 'string') {
              // 如果是 base64 编码
              const binaryString = atob(message.data);
              flvData = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                flvData[i] = binaryString.charCodeAt(i);
              }
            } else if (Array.isArray(message.data)) {
              flvData = new Uint8Array(message.data);
            } else {
              flvData = new Uint8Array(message.data);
            }
            handleFlvData(flvData);
          }
        }
      } else {
        // 二进制数据，直接作为 FLV 数据处理
        console.log("收到二进制数据，长度:", event.data.byteLength);
        const flvData = new Uint8Array(event.data);
        handleFlvData(flvData);
      }
    } catch (error) {
      console.error("处理消息时出错:", error);
    }
  };
  
  ws.onerror = (error) => {
    console.error("WebSocket 错误:", error);
    isConnected.value = false;
    connectionStatus.value = "连接错误";
  };
  
  ws.onclose = (event) => {
    console.log("WebSocket 已关闭", event.code, event.reason);
    isConnected.value = false;
    isStreaming.value = false;
    connectionStatus.value = "连接已关闭";
    
    // 自动重连（可选）
    setTimeout(() => {
      if (!isConnected.value) {
        console.log("尝试重新连接...");
        connectWebSocket();
      }
    }, 3000);
  };
}

onMounted(() => {
  connectWebSocket();
});

onBeforeUnmount(() => {
  // 停止推流
  if (isStreaming.value) {
    stopStream();
  }
  
  // 销毁播放器
  destroyPlayer();
  
  // 关闭 WebSocket
  if (ws) {
    ws.close();
    ws = null;
  }
});
</script>

<template>
  <div class="video-stream-container">
    <div class="control-panel">
      <div class="status">
        <span>连接状态: {{ connectionStatus }}</span>
        <span :class="{ connected: isConnected, disconnected: !isConnected }">
          {{ isConnected ? '●' : '○' }}
        </span>
      </div>
      
      <div class="buttons">
        <button 
          @click="startStream" 
          :disabled="!isConnected || isStreaming"
          class="start-btn"
        >
          开始推流
        </button>
        
        <button 
          @click="stopStream" 
          :disabled="!isConnected || !isStreaming"
          class="stop-btn"
        >
          停止推流
        </button>
        
        <button 
          @click="connectWebSocket" 
          :disabled="isConnected"
          class="connect-btn"
        >
          重新连接
        </button>
      </div>
    </div>
    
    <div class="video-container">
      <video 
        ref="videoRef" 
        controls 
        muted 
        autoplay
        class="video-player"
        poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQ1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuetieW+heaOqOa1gTwvdGV4dD48L3N2Zz4="
      >
        您的浏览器不支持视频播放
      </video>
    </div>
  </div>
</template>

<style scoped>
.video-stream-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.control-panel {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.status {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.connected {
  color: #52c41a;
}

.disconnected {
  color: #ff4d4f;
}

.buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.start-btn {
  background: #52c41a;
  color: white;
}

.start-btn:hover:not(:disabled) {
  background: #73d13d;
}

.stop-btn {
  background: #ff4d4f;
  color: white;
}

.stop-btn:hover:not(:disabled) {
  background: #ff7875;
}

.connect-btn {
  background: #1890ff;
  color: white;
}

.connect-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.video-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: auto;
  min-height: 400px;
  background: #000;
  display: block;
}

@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
  
  .video-player {
    min-height: 250px;
  }
}
</style>
