/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.122
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as st}from"./chunk-XTD56O4T.js";import{b as at,c as ht,d as nt}from"./chunk-3WTHA73F.js";import{a as et}from"./chunk-K3VQHMI6.js";import{a as h,b as tt,c as v,d as vt}from"./chunk-UTRPTI5S.js";import{a as Y}from"./chunk-TFNGIACM.js";import{a as xt}from"./chunk-ISVCOS3X.js";import{a as K}from"./chunk-OW23VKVW.js";import{b as C}from"./chunk-LKAZ42NI.js";import{e as D}from"./chunk-MYHWD27O.js";var ct={CLOCKWISE:xt.CW,COUNTER_CLOCKWISE:xt.CCW};ct.validate=function(t){return t===ct.CLOCKWISE||t===ct.COUNTER_CLOCKWISE};var pt=Object.freeze(ct);function dt(t,n,e=2){let c=n&&n.length,r=c?n[0]*e:t.length,o=bt(t,0,r,e,!0),i=[];if(!o||o.next===o.prev)return i;let s,f,u;if(c&&(o=It(t,n,o,e)),t.length>80*e){s=1/0,f=1/0;let a=-1/0,y=-1/0;for(let d=e;d<r;d+=e){let S=t[d],p=t[d+1];S<s&&(s=S),p<f&&(f=p),S>a&&(a=S),p>y&&(y=p)}u=Math.max(a-s,y-f),u=u!==0?32767/u:0}return rt(o,i,e,s,f,u,0),i}function bt(t,n,e,c,r){let o;if(r===jt(t,n,e,c)>0)for(let i=n;i<e;i+=c)o=wt(i/c|0,t[i],t[i+1],o);else for(let i=e-c;i>=n;i-=c)o=wt(i/c|0,t[i],t[i+1],o);return o&&ft(o,o.next)&&(ot(o),o=o.next),o}function N(t,n){if(!t)return t;n||(n=t);let e=t,c;do if(c=!1,!e.steiner&&(ft(e,e.next)||b(e.prev,e,e.next)===0)){if(ot(e),e=n=e.prev,e===e.next)break;c=!0}else e=e.next;while(c||e!==n);return n}function rt(t,n,e,c,r,o,i){if(!t)return;!i&&o&&Nt(t,c,r,o);let s=t;for(;t.prev!==t.next;){let f=t.prev,u=t.next;if(o?Rt(t,c,r,o):Pt(t)){n.push(f.i,t.i,u.i),ot(t),t=u.next,s=u.next;continue}if(t=u,t===s){i?i===1?(t=zt(N(t),n),rt(t,n,e,c,r,o,2)):i===2&&Bt(t,n,e,c,r,o):rt(N(t),n,e,c,r,o,1);break}}}function Pt(t){let n=t.prev,e=t,c=t.next;if(b(n,e,c)>=0)return!1;let r=n.x,o=e.x,i=c.x,s=n.y,f=e.y,u=c.y,a=r<o?r<i?r:i:o<i?o:i,y=s<f?s<u?s:u:f<u?f:u,d=r>o?r>i?r:i:o>i?o:i,S=s>f?s>u?s:u:f>u?f:u,p=c.next;for(;p!==n;){if(p.x>=a&&p.x<=d&&p.y>=y&&p.y<=S&&V(r,s,o,f,i,u,p.x,p.y)&&b(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function Rt(t,n,e,c){let r=t.prev,o=t,i=t.next;if(b(r,o,i)>=0)return!1;let s=r.x,f=o.x,u=i.x,a=r.y,y=o.y,d=i.y,S=s<f?s<u?s:u:f<u?f:u,p=a<y?a<d?a:d:y<d?y:d,T=s>f?s>u?s:u:f>u?f:u,L=a>y?a>d?a:d:y>d?y:d,z=yt(S,p,n,e,c),F=yt(T,L,n,e,c),l=t.prevZ,x=t.nextZ;for(;l&&l.z>=z&&x&&x.z<=F;){if(l.x>=S&&l.x<=T&&l.y>=p&&l.y<=L&&l!==r&&l!==i&&V(s,a,f,y,u,d,l.x,l.y)&&b(l.prev,l,l.next)>=0||(l=l.prevZ,x.x>=S&&x.x<=T&&x.y>=p&&x.y<=L&&x!==r&&x!==i&&V(s,a,f,y,u,d,x.x,x.y)&&b(x.prev,x,x.next)>=0))return!1;x=x.nextZ}for(;l&&l.z>=z;){if(l.x>=S&&l.x<=T&&l.y>=p&&l.y<=L&&l!==r&&l!==i&&V(s,a,f,y,u,d,l.x,l.y)&&b(l.prev,l,l.next)>=0)return!1;l=l.prevZ}for(;x&&x.z<=F;){if(x.x>=S&&x.x<=T&&x.y>=p&&x.y<=L&&x!==r&&x!==i&&V(s,a,f,y,u,d,x.x,x.y)&&b(x.prev,x,x.next)>=0)return!1;x=x.nextZ}return!0}function zt(t,n){let e=t;do{let c=e.prev,r=e.next.next;!ft(c,r)&&St(c,e,e.next,r)&&it(c,r)&&it(r,c)&&(n.push(c.i,e.i,r.i),ot(e),ot(e.next),e=t=r),e=e.next}while(e!==t);return N(e)}function Bt(t,n,e,c,r,o){let i=t;do{let s=i.next.next;for(;s!==i.prev;){if(i.i!==s.i&&_t(i,s)){let f=At(i,s);i=N(i,i.next),f=N(f,f.next),rt(i,n,e,c,r,o,0),rt(f,n,e,c,r,o,0);return}s=s.next}i=i.next}while(i!==t)}function It(t,n,e,c){let r=[];for(let o=0,i=n.length;o<i;o++){let s=n[o]*c,f=o<i-1?n[o+1]*c:t.length,u=bt(t,s,f,c,!1);u===u.next&&(u.steiner=!0),r.push(Ut(u))}r.sort(Wt);for(let o=0;o<r.length;o++)e=$t(r[o],e);return e}function Wt(t,n){return t.x-n.x}function $t(t,n){let e=Gt(t,n);if(!e)return n;let c=At(e,t);return N(c,c.next),N(e,e.next)}function Gt(t,n){let e=n,c=t.x,r=t.y,o=-1/0,i;do{if(r<=e.y&&r>=e.next.y&&e.next.y!==e.y){let y=e.x+(r-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(y<=c&&y>o&&(o=y,i=e.x<e.next.x?e:e.next,y===c))return i}e=e.next}while(e!==n);if(!i)return null;let s=i,f=i.x,u=i.y,a=1/0;e=i;do{if(c>=e.x&&e.x>=f&&c!==e.x&&V(r<u?c:o,r,f,u,r<u?o:c,r,e.x,e.y)){let y=Math.abs(r-e.y)/(c-e.x);it(e,t)&&(y<a||y===a&&(e.x>i.x||e.x===i.x&&Ht(i,e)))&&(i=e,a=y)}e=e.next}while(e!==s);return i}function Ht(t,n){return b(t.prev,t,n.prev)<0&&b(n.next,t,t.next)<0}function Nt(t,n,e,c){let r=t;do r.z===0&&(r.z=yt(r.x,r.y,n,e,c)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,kt(r)}function kt(t){let n,e=1;do{let c=t,r;t=null;let o=null;for(n=0;c;){n++;let i=c,s=0;for(let u=0;u<e&&(s++,i=i.nextZ,!!i);u++);let f=e;for(;s>0||f>0&&i;)s!==0&&(f===0||!i||c.z<=i.z)?(r=c,c=c.nextZ,s--):(r=i,i=i.nextZ,f--),o?o.nextZ=r:t=r,r.prevZ=o,o=r;c=i}o.nextZ=null,e*=2}while(n>1);return t}function yt(t,n,e,c,r){return t=(t-e)*r|0,n=(n-c)*r|0,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,n=(n|n<<8)&16711935,n=(n|n<<4)&252645135,n=(n|n<<2)&858993459,n=(n|n<<1)&1431655765,t|n<<1}function Ut(t){let n=t,e=t;do(n.x<e.x||n.x===e.x&&n.y<e.y)&&(e=n),n=n.next;while(n!==t);return e}function V(t,n,e,c,r,o,i,s){return(r-i)*(n-s)>=(t-i)*(o-s)&&(t-i)*(c-s)>=(e-i)*(n-s)&&(e-i)*(o-s)>=(r-i)*(c-s)}function _t(t,n){return t.next.i!==n.i&&t.prev.i!==n.i&&!Kt(t,n)&&(it(t,n)&&it(n,t)&&Vt(t,n)&&(b(t.prev,t,n.prev)||b(t,n.prev,n))||ft(t,n)&&b(t.prev,t,t.next)>0&&b(n.prev,n,n.next)>0)}function b(t,n,e){return(n.y-t.y)*(e.x-n.x)-(n.x-t.x)*(e.y-n.y)}function ft(t,n){return t.x===n.x&&t.y===n.y}function St(t,n,e,c){let r=lt(b(t,n,e)),o=lt(b(t,n,c)),i=lt(b(e,c,t)),s=lt(b(e,c,n));return!!(r!==o&&i!==s||r===0&&ut(t,e,n)||o===0&&ut(t,c,n)||i===0&&ut(e,t,c)||s===0&&ut(e,n,c))}function ut(t,n,e){return n.x<=Math.max(t.x,e.x)&&n.x>=Math.min(t.x,e.x)&&n.y<=Math.max(t.y,e.y)&&n.y>=Math.min(t.y,e.y)}function lt(t){return t>0?1:t<0?-1:0}function Kt(t,n){let e=t;do{if(e.i!==t.i&&e.next.i!==t.i&&e.i!==n.i&&e.next.i!==n.i&&St(e,e.next,t,n))return!0;e=e.next}while(e!==t);return!1}function it(t,n){return b(t.prev,t,t.next)<0?b(t,n,t.next)>=0&&b(t,t.prev,n)>=0:b(t,n,t.prev)<0||b(t,t.next,n)<0}function Vt(t,n){let e=t,c=!1,r=(t.x+n.x)/2,o=(t.y+n.y)/2;do e.y>o!=e.next.y>o&&e.next.y!==e.y&&r<(e.next.x-e.x)*(o-e.y)/(e.next.y-e.y)+e.x&&(c=!c),e=e.next;while(e!==t);return c}function At(t,n){let e=mt(t.i,t.x,t.y),c=mt(n.i,n.x,n.y),r=t.next,o=n.prev;return t.next=n,n.prev=t,e.next=r,r.prev=e,c.next=e,e.prev=c,o.next=c,c.prev=o,c}function wt(t,n,e,c){let r=mt(t,n,e);return c?(r.next=c.next,r.prev=c,c.next.prev=r,c.next=r):(r.prev=r,r.next=r),r}function ot(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function mt(t,n,e){return{i:t,x:n,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function jt(t,n,e,c){let r=0;for(let o=n,i=e-c;o<e;o+=c)r+=(t[i]-t[o])*(t[o+1]+t[i+1]),i=o;return r}var qt=new h,Jt=new h,B={};B.computeArea2D=function(t){C.defined("positions",t),C.typeOf.number.greaterThanOrEquals("positions.length",t.length,3);let n=t.length,e=0;for(let c=n-1,r=0;r<n;c=r++){let o=t[c],i=t[r];e+=o.x*i.y-i.x*o.y}return e*.5};B.computeWindingOrder2D=function(t){return B.computeArea2D(t)>0?pt.COUNTER_CLOCKWISE:pt.CLOCKWISE};B.triangulate=function(t,n){C.defined("positions",t);let e=v.packArray(t);return dt(e,n,2)};var Tt=new h,Mt=new h,Zt=new h,Ct=new h,Et=new h,Ot=new h,R=new h,Lt=new v,Dt=new v,Ft=new v,j=new v;B.computeSubdivision=function(t,n,e,c,r){r=K(r,Y.RADIANS_PER_DEGREE);let o=D(c);C.typeOf.object("ellipsoid",t),C.defined("positions",n),C.defined("indices",e),C.typeOf.number.greaterThanOrEquals("indices.length",e.length,3),C.typeOf.number.equals("indices.length % 3","0",e.length%3,0),C.typeOf.number.greaterThan("granularity",r,0);let i=e.slice(0),s,f=n.length,u=new Array(f*3),a=new Array(f*2),y=0,d=0;for(s=0;s<f;s++){let l=n[s];if(u[y++]=l.x,u[y++]=l.y,u[y++]=l.z,o){let x=c[s];a[d++]=x.x,a[d++]=x.y}}let S=[],p={},T=t.maximumRadius,L=Y.chordLength(r,T),z=L*L;for(;i.length>0;){let l=i.pop(),x=i.pop(),m=i.pop(),A=h.fromArray(u,m*3,Tt),E=h.fromArray(u,x*3,Mt),q=h.fromArray(u,l*3,Zt),J,Q,I;o&&(J=v.fromArray(a,m*2,Lt),Q=v.fromArray(a,x*2,Dt),I=v.fromArray(a,l*2,Ft));let k=h.multiplyByScalar(h.normalize(A,Ct),T,Ct),U=h.multiplyByScalar(h.normalize(E,Et),T,Et),W=h.multiplyByScalar(h.normalize(q,Ot),T,Ot),$=h.magnitudeSquared(h.subtract(k,U,R)),G=h.magnitudeSquared(h.subtract(U,W,R)),X=h.magnitudeSquared(h.subtract(W,k,R)),H=Math.max($,G,X),M,w,g;H>z?$===H?(M=`${Math.min(m,x)} ${Math.max(m,x)}`,s=p[M],D(s)||(w=h.add(A,E,R),h.multiplyByScalar(w,.5,w),u.push(w.x,w.y,w.z),s=u.length/3-1,p[M]=s,o&&(g=v.add(J,Q,j),v.multiplyByScalar(g,.5,g),a.push(g.x,g.y))),i.push(m,s,l),i.push(s,x,l)):G===H?(M=`${Math.min(x,l)} ${Math.max(x,l)}`,s=p[M],D(s)||(w=h.add(E,q,R),h.multiplyByScalar(w,.5,w),u.push(w.x,w.y,w.z),s=u.length/3-1,p[M]=s,o&&(g=v.add(Q,I,j),v.multiplyByScalar(g,.5,g),a.push(g.x,g.y))),i.push(x,s,m),i.push(s,l,m)):X===H&&(M=`${Math.min(l,m)} ${Math.max(l,m)}`,s=p[M],D(s)||(w=h.add(q,A,R),h.multiplyByScalar(w,.5,w),u.push(w.x,w.y,w.z),s=u.length/3-1,p[M]=s,o&&(g=v.add(I,J,j),v.multiplyByScalar(g,.5,g),a.push(g.x,g.y))),i.push(l,s,x),i.push(s,m,x)):(S.push(m),S.push(x),S.push(l))}let F={attributes:{position:new nt({componentDatatype:et.DOUBLE,componentsPerAttribute:3,values:u})},indices:S,primitiveType:at.TRIANGLES};return o&&(F.attributes.st=new nt({componentDatatype:et.FLOAT,componentsPerAttribute:2,values:a})),new ht(F)};var Qt=new tt,Xt=new tt,Yt=new tt,gt=new tt;B.computeRhumbLineSubdivision=function(t,n,e,c,r){r=K(r,Y.RADIANS_PER_DEGREE);let o=D(c);C.typeOf.object("ellipsoid",t),C.defined("positions",n),C.defined("indices",e),C.typeOf.number.greaterThanOrEquals("indices.length",e.length,3),C.typeOf.number.equals("indices.length % 3","0",e.length%3,0),C.typeOf.number.greaterThan("granularity",r,0);let i=e.slice(0),s,f=n.length,u=new Array(f*3),a=new Array(f*2),y=0,d=0;for(s=0;s<f;s++){let m=n[s];if(u[y++]=m.x,u[y++]=m.y,u[y++]=m.z,o){let A=c[s];a[d++]=A.x,a[d++]=A.y}}let S=[],p={},T=t.maximumRadius,L=Y.chordLength(r,T),z=new st(void 0,void 0,t),F=new st(void 0,void 0,t),l=new st(void 0,void 0,t);for(;i.length>0;){let m=i.pop(),A=i.pop(),E=i.pop(),q=h.fromArray(u,E*3,Tt),J=h.fromArray(u,A*3,Mt),Q=h.fromArray(u,m*3,Zt),I,k,U;o&&(I=v.fromArray(a,E*2,Lt),k=v.fromArray(a,A*2,Dt),U=v.fromArray(a,m*2,Ft));let W=t.cartesianToCartographic(q,Qt),$=t.cartesianToCartographic(J,Xt),G=t.cartesianToCartographic(Q,Yt);z.setEndPoints(W,$);let X=z.surfaceDistance;F.setEndPoints($,G);let H=F.surfaceDistance;l.setEndPoints(G,W);let M=l.surfaceDistance,w=Math.max(X,H,M),g,P,_,Z,O;w>L?X===w?(g=`${Math.min(E,A)} ${Math.max(E,A)}`,s=p[g],D(s)||(P=z.interpolateUsingFraction(.5,gt),_=(W.height+$.height)*.5,Z=h.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),s=u.length/3-1,p[g]=s,o&&(O=v.add(I,k,j),v.multiplyByScalar(O,.5,O),a.push(O.x,O.y))),i.push(E,s,m),i.push(s,A,m)):H===w?(g=`${Math.min(A,m)} ${Math.max(A,m)}`,s=p[g],D(s)||(P=F.interpolateUsingFraction(.5,gt),_=($.height+G.height)*.5,Z=h.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),s=u.length/3-1,p[g]=s,o&&(O=v.add(k,U,j),v.multiplyByScalar(O,.5,O),a.push(O.x,O.y))),i.push(A,s,E),i.push(s,m,E)):M===w&&(g=`${Math.min(m,E)} ${Math.max(m,E)}`,s=p[g],D(s)||(P=l.interpolateUsingFraction(.5,gt),_=(G.height+W.height)*.5,Z=h.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),s=u.length/3-1,p[g]=s,o&&(O=v.add(U,I,j),v.multiplyByScalar(O,.5,O),a.push(O.x,O.y))),i.push(m,s,A),i.push(s,E,A)):(S.push(E),S.push(A),S.push(m))}let x={attributes:{position:new nt({componentDatatype:et.DOUBLE,componentsPerAttribute:3,values:u})},indices:S,primitiveType:at.TRIANGLES};return o&&(x.attributes.st=new nt({componentDatatype:et.FLOAT,componentsPerAttribute:2,values:a})),new ht(x)};B.scaleToGeodeticHeight=function(t,n,e,c){e=K(e,vt.default);let r=qt,o=Jt;if(n=K(n,0),c=K(c,!0),D(t)){let i=t.length;for(let s=0;s<i;s+=3)h.fromArray(t,s,o),c&&(o=e.scaleToGeodeticSurface(o,o)),n!==0&&(r=e.geodeticSurfaceNormal(o,r),h.multiplyByScalar(r,n,r),h.add(o,r,o)),t[s]=o.x,t[s+1]=o.y,t[s+2]=o.z}return t};var ge=B;export{pt as a,ge as b};
